import { Injectable } from '@nestjs/common';
import {
  randomBytes,
  createCipheriv,
  createDecipheriv,
  createHash,
} from 'crypto';
import * as AES256 from 'aes-everywhere';
// import AES256 from 'src/card/bridgecard-aes';
import config from 'src/config';

@Injectable()
export class EncryptionService {
  // Encryption and decryption key. Must be 256 bits (32 characters).
  static IV_LENGTH = 16; // For AES, this is always 16

  getKeyBuffer(key: string) {
    const hash = createHash('sha256');
    hash.update(key);
    return hash.digest();
  }

  //   encrypt(key: string, text: string) {
  //     let iv = randomBytes(IV_LENGTH);

  //     let cipher = createCipheriv('aes-256-cbc', this.getKeyBuffer(key), iv);
  //     let encrypted = cipher.update(text);

  //     encrypted = Buffer.concat([encrypted, cipher.final()]);

  //     return iv.toString('hex') + ':' + encrypted.toString('hex');
  //   }

  encryptDeep(key: string, text: string) {
    try {
      const iv = randomBytes(EncryptionService.IV_LENGTH);

      const cipher = createCipheriv('aes-256-cbc', this.getKeyBuffer(key), iv);
      let encrypted = cipher.update(text);

      encrypted = Buffer.concat([encrypted, cipher.final()]);

      return iv.toString('hex') + ':' + encrypted.toString('hex');
    } catch {
      return text;
    }
  }

  decrypt(text: string) {
    const textParts = text.split(':');
    const iv = Buffer.from(textParts.shift(), 'hex');
    const encryptedText = Buffer.from(textParts.join(':'), 'hex');
    const decipher = createDecipheriv(
      'aes-256-cbc',
      this.getKeyBuffer(config.bridgeCard.sk),
      iv,
    );
    let decrypted = decipher.update(encryptedText);

    decrypted = Buffer.concat([decrypted, decipher.final()]);

    return decrypted.toString();
  }

  decryptDeep(key: string, text: string) {
    try {
      const textParts = text.split(':');
      const iv = Buffer.from(textParts.shift(), 'hex');
      const encryptedText = Buffer.from(textParts.join(':'), 'hex');
      const decipher = createDecipheriv(
        'aes-256-cbc',
        this.getKeyBuffer(key),
        iv,
      );
      let decrypted = decipher.update(encryptedText);

      decrypted = Buffer.concat([decrypted, decipher.final()]);

      return decrypted.toString();
    } catch {
      return text;
    }
  }

  encryptBridgeCardPin(pin: string) {
    return AES256.encrypt(pin, config.bridgeCard.sk);
  }

  decryptBridgecard(key: string, text: string) {
    return AES256.decrypt(text, config.bridgeCard.sk);
  }

  encryptSensitiveData(data: any, key: string) {
    data.cvv = this.encryptDeep(key, data.cvv);
    data.expiry_month =
      data.expiry_month && this.encryptDeep(key, data.expiry_month);
    data.expiryMonth =
      data.expiryMonth && this.encryptDeep(key, data.expiryMonth);
    data.expiry_year =
      data.expiry_year && this.encryptDeep(key, data.expiry_year);
    data.expiryYear = data.expiryYear && this.encryptDeep(key, data.expiryYear);
    data.card_number =
      data.card_number && this.encryptDeep(key, data.card_number);
    data.cardNumber = data.cardNumber && this.encryptDeep(key, data.cardNumber);

    return data;
  }

  decryptSensitiveData(data: any, key: string) {
    data.cvv = this.decryptDeep(key, data.cvv);
    data.expiry_month =
      data.expiry_month && this.decryptDeep(key, data.expiry_month);
    data.expiryMonth =
      data.expiryMonth && this.decryptDeep(key, data.expiryMonth);
    data.expiry_year =
      data.expiry_year && this.decryptDeep(key, data.expiry_year);
    data.expiryYear = data.expiryYear && this.decryptDeep(key, data.expiryYear);
    data.card_number =
      data.card_number && this.decryptDeep(key, data.card_number);
    data.cardNumber = data.cardNumber && this.decryptDeep(key, data.cardNumber);

    return data;
  }

  decryptMidenAES(
    cipherText: string,
    clientId: string,
  ): {
    CardNumber: string;
    SecurityCode: string;
    Expiration: string;
  } {
    const iv = Buffer.from(clientId.slice(0, 16), 'utf8');
    const key = Buffer.from(clientId.slice(-16), 'utf8');
    const encrypted = Buffer.from(cipherText, 'base64');

    try {
      const decipher = createDecipheriv('aes-128-cbc', key, iv);
      let decrypted = decipher.update(encrypted);
      decrypted = Buffer.concat([decrypted, decipher.final()]);

      return JSON.parse(decrypted.toString());
    } catch (err) {
      console.log(err);
      return null;
    }
  }
}
