export interface MidenResponse {
  data: any;
  isSuccessful: boolean;
  responseMessage: string;
  responseCode: string;
}

export interface MidenTokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
}

export interface MidenFxRateResponse {
  sourceCurrency: string;
  destinationCurrency: string;
  buyRate: number;
  sellRate: number;
}

export interface CreateCardDto {
  firstName: string;
  lastName: string;
  phone: string;
  address1: string;
  address2?: string;
  email: string;
  city: string;
  state: string;
  zipcode: string;
  country: 'NG';
  //cardClass : PrepaidConsumer,
  idNumber: string;
  idType: 'BVN';
  customerBvn: string;
  initialBalance: number;
  cardBrand: 'Mastercard';
  clientReference: string;
  cardCurrency: 'USD' | 'NGN';
  walletCurrency: 'USD' | 'NGN';
}

export interface MidenCardResponse {
  message?: string;
  data: {
    cardCustomerId: string;
    cardId: string;
    cardBrand: string;
    cardClass: string;
    // cardNumber: string;
    // securityCode: string;
    limitWindow: string;
    // expirationDate: string;
    expiration: string;
    terminateDate: string;
    currencyCode: string;
    firstSix: string;
    lastFour: string;
    status: string;
    availableBalance: number;
    isPhysical: boolean;
    secureCardDetails: string;
  };
  amountDebited: number;
  rate: number;
  transactionWallet: string;
  isSuccessful: boolean;
  responseMessage: string;
  responseCode: string;
}

export interface MidenCardDetailsResponse {
  data: {
    // cardNumber: string;
    // securityCode: string;
    amountLimit: number;
    usageLimit: number;
    limitWindow: string;
    // expirationDate: string;
    expiration: string;
    terminateDate: string;
    currencyCode: string;
    firstSix: string;
    lastFour: string;
    nameLine1: string;
    nameLine2: string;
    status: string;
    bank: number;
    issuedAmount: number;
    cardType: string;
    purchaseType: number;
    midWhitelist: [];
    midBlacklist: [string];
    gatewayMerchantGuid: string;
    availableBalance: 10;
    isPhysical: false;
    isLodged: true;
    fingerprint: string;
    balanceAsAtTermination: number;
    secureCardDetails: string;
    billingDetails: {
      firstName: string;
      lastName: string;
      addressLine1: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
  };
  cardTransactionId: number;
  amountDebited: number;
  rate: number;
  balanceBeforeTermination: number;
  blockedByAdmin: boolean;
  isSuccessful: boolean;
  responseMessage: string;
  responseCode: string;
}

export interface MidenFreezeUnfreezeCardResponse {
  cardId: string;
  cardBrand: string;
  cardClass: string;
  limitWindow: string;
  expirationDate: string;
  expiration: string;
  terminateDate: string;
  currencyCode: string;
  firstSix: string;
  lastFour: string;
  status: string;
  midWhitelist: [];
  midBlacklist: [];
  gatewayMerchantGuid: string;
  availableBalance: number;
  isPhysical: boolean;
}

export interface MidenTopUpResponse {
  data: {
    amountLimit: number;
    cardId: string;
    cardBrand: string;
    cardClass: string;
    limitWindow: string;
    expirationDate: string;
    expiration: string;
    terminateDate: string;
    currencyCode: string;
    firstSix: string;
    lastFour: string;
    status: string;
    midWhitelist: [];
    midBlacklist: [];
    gatewayMerchantGuid: string;
    availableBalance: number;
    isPhysical: boolean;
  };
  cardTransactionId: number;
  amountDebited: number;
  rate: number;
  transactionWallet: string;
  balanceBeforeTermination: number;
  blockedByAdmin: boolean;
  isSuccessful: boolean;
  responseMessage: string;
  responseCode: string;
}

export interface MidenCardTransactionResponse {
  isSuccessful: boolean;
  responseMessage: string;
  responseCode: string;
  transaction: {
    id: number;
    cardId: string;
    uniqueKey: string;
    merchantName: string;
    merchantCity: string;
    merchantState: string;
    merchantMcc: string;
    merchantMid: string;
    merchantCountry: string;
    transactionType: string;
    transactionStatus: string;
    transactionReference: string;
    transactionAmount: number;
    currencyCode: string;
    transactionTime: string;
    description: string;
    network: string;
    authorizationAmount: number;
    authorizationCurrencyCode: string;
    balanceAfterTransaction: number;
    cardGuid: string;
    authCode: string;
    createdAt: string;
    updatedAt: string;
    maskPan: string;
    cardCurrency: string;
    authorizationTransactionId: number;
  };
}

export interface MidenWebhookResponse {
  cardId: string;
  eventId: string;
  eventClass: string;
  eventTime: string;
  eventType: string;
  uniqueKey: string;
  data: any;
}

export interface MidenCardCustomersResponse {
  isSuccessful: boolean;
  responseMessage: string;
  responseCode: string;
  customers: [
    {
      customerId: string;
      firstName: string;
      lastName: string;
      phone: string;
      address1: string;
      address2: string;
      city: string;
      state: string;
      zipcode: string;
      country: string;
      idNumber: string;
      idType: string;
      imageUrl: string;
      uniqueKey: string;
      status: string;
      customerBvn: string;
      customerName: string;
      organizationName: string;
      customerDateOfBirth: string;
      createdAt: string;
      updatedAt: string;
      customerEmail: string;
      customerCards: MidenCard[];
    },
  ];
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

export interface MidenCard {
  cardId: string;
  customerId: string;
  maskPan: string;
  payee: string;
  usageLimit: number;
  amountLimit: number;
  limitWindow: string;
  expirationDate: string;
  terminateDate: string;
  purchaseType: string;
  sequenceNumber: string;
  orderNumber: string;
  expiration: string;
  currencyCode: string;
  status: string;
  midWhitelist: string;
  midBlacklist: string;
  availableBalance: number;
  activated: boolean;
  cardBrand: string;
  cardClass: string;
  organizationName: string;
  uniqueKey: string;
  isPhysical: boolean;
  isLodged: boolean;
  createdAt: string;
  updatedAt: string;
  cardType: string;
  deactivationDate: string;
  terminated: boolean;
  terminationDate: string;
  cancelled: boolean;
  cancellationDate: string;
  cardGuid: string;
  blockedByAdmin: boolean;
  freezeReason: string;
  terminationReason: string;
  googlePayEnabled: boolean;
  applePayEnabled: boolean;
  threeDSecureEnabled: boolean;
  airlinePaymentEnabled: boolean;
}


export interface MidenCardTransactionsResponse {
  isSuccessful: boolean,
  responseMessage: string,
  responseCode: string,
  transactions: [
    {
      id: number,
      cardId: string,
      cardGuid: string,
      uniqueKey: string,
      merchantName: string,
      merchantCity: string,
      merchantState: string,
      merchantMcc: string,
      merchantMid: string,
      merchantCountry: string,
      transactionType: string,
      transactionStatus: string,
      transactionAmount: number,
      currencyCode: string,
      transactionTime: Date,
      description: string,
      network: string,
      authorizationAmount: number,
      authorizationCurrencyCode: string,
      balanceAfterTransaction: number,
      createdAt: string,
      updatedAt: string
    }
  ],
  currentPage: number,
  pageSize: number,
  totalCount: number,
  totalPages: number
}