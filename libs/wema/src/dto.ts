export interface WemaResponse {
  data: any;
  message: string;
  status: boolean;
  code: number;
  statusCode: number;
}

export interface CreateVirtualCardDto {
  emailaddress: string;
  phoneNumber: string;
  // amount: number;
  accountNo: string;
  customerAddress: string;
  cardKey: string;
  currency: string;
  CustomerState: string;
}

export interface WemaCardResponse {
  data: string; // CMP reference number
  message: string;
  status: boolean;
  code: number;
  statusCode: number;
}

export interface WemaCardDetailsResponse {
  data: string; // Encrypted string to be decrypted
  message: string;
  status: boolean;
  code: number;
  statusCode: number;
}

export interface WemaCardDetails {
  balance: number;
  name: string;
  pan: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  cvv: string;
  expiry: string;
  linkedAccount: string;
  status: number;
}

export interface ActivateCardDto {
  accountNumber: string;
  newPin: string;
  expiryDate: string;
  emailAddress: string;
  fullPan: string;
}

export interface ActivateCardResponse {
  successful: boolean;
  message: string;
}

export interface AccountValidationResponse {
  result: {
    bankCode: string;
    accountName: string;
    accountNumber: string;
    currency: string;
  };
  errorMessage: string;
  errorMessages: string[];
  hasError: boolean;
  timeGenerated: string;
}

export interface FundWalletDto {
  securityInfo: string;
  destinationAccountNumber: string;
  amount: number;
  narration: string;
  transactionReference: string;
  useCustomNarration: boolean;
}

export interface FundWalletResponse {
  result: {
    status: string;
    message: string;
    narration: string;
    transactionReference: string;
    platformTransactionReference: string;
    transactionStan: string;
    orinalTxnTransactionDate: string;
  };
  errorMessage: string;
  errorMessages: string[];
  hasError: boolean;
  timeGenerated: string;
}

export interface TransactionData {
  status: string;
  message: string;
  narration: string;
  transactionReference: string;
  platformTransactionReference: string;
  transactionStan: string;
  orinalTxnTransactionDate: string;
}

export interface TransactionResult {
  title: string;
  message: string;
  data: TransactionData;
  request: number;
}

export interface TransactionResponse {
  result: TransactionResult;
  errorMessage: string;
  errorMessages: string[];
  hasError: boolean;
  timeGenerated: string;
}

export interface WalletInfoResult {
  walletNumber: string;
  availableBalance: string;
  walletStatus: string;
  accountType: string;
}

export interface WalletInfoResponse {
  result: WalletInfoResult;
  successful: boolean;
  message: string;
}

export interface TransactionHistoryQuery {
  accountNumber: string; // required
  from?: string; // optional, date-time string
  to?: string; // optional, date-time string
  keyWord?: string; // optional
}

export interface DebitWalletDto {
  securityInfo: string;
  amount: number;
  destinationAccountNumber: string;
  destinationBankCode: string;
  destinationBankName: string;
  destinationAccountName: string;
  sourceAccountNumber: string;
  narration: string;
  transactionReference: string;
  useCustomNarration: boolean;
}
