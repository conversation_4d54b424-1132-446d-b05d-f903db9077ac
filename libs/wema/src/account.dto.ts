// DTOs for Wema Bank Account APIs

// Generate Partnership Account
export interface GeneratePartnershipAccountDto {
  bvn: string;
  nin: string;
  phoneNumber: string;
  emailAddress: string;
  residentialAddress: {
    buildingNumber: string;
    apartment?: string;
    street: string;
    city: string;
    town?: string;
    state: string;
    lga: string;
    lcda?: string;
    landmark?: string;
    additionalInformation?: string;
    country: string;
    fullAddress: string;
    postalCode?: string;
  };
  liveImageOfFace: string;
}

export interface GeneratePartnershipAccountResponse {
  data: {
    accountGenerationStatus: string | null;
    trackingId: string;
    addressVerificationStatus: string | null;
  };
  message: string;
  status: boolean;
  code: number;
  statusCode: number;
  errors: any | null;
}

// Validate OTP and Generate Partnership Account
export interface ValidateOtpAndGenerateAccountDto {
  phoneNumber: string;
  otp: string;
  trackingId: string;
}

export interface ValidateOtpAndGenerateAccountResponse {
  data: {
    accountGenerationStatus: string;
  };
  message: string;
  status: boolean;
  code: number;
  statusCode: number;
  errors: any | null;
}

// Resend OTP Request
export interface ResendOtpRequestDto {
  phoneNumber: string;
  trackingId: string;
}

export interface ResendOtpRequestResponse {
  data: {
    accountGenerationStatus: string | null;
    trackingId: string;
    addressVerificationStatus: string | null;
  };
  message: string;
  status: boolean;
  code: number;
  statusCode: number;
  errors: any | null;
}

// Get Partnership Account Details
export interface GetPartnershipAccountDetailsParams {
  accountNumber: string;
  partnerCode: string;
}

export interface GetPartnershipAccountDetailsResponse {
  status: boolean;
  message: string;
  code: string;
  data: {
    accountNumber: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    email: string;
  };
  statusCode: string;
}

// Submit Partner Address
export interface SubmitPartnerAddressDto {
  accountNumber: string;
  residentialAddress: {
    buildingNumber: string;
    apartment?: string;
    street: string;
    city: string;
    town?: string;
    state: string;
    lga: string;
    lcda?: string;
    landmark?: string;
    additionalInformation?: string;
    country: string;
    fullAddress: string;
    postalCode?: string;
  };
}

export interface SubmitPartnerAddressResponse {
  data: {
    accountGenerationStatus: string | null;
    trackingId: string;
    addressVerificationStatus: string | null;
  };
  message: string;
  status: boolean;
  code: number;
  statusCode: number;
  errors: any | null;
}
