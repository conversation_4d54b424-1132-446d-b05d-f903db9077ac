import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';
import config from 'src/config';

interface SecurityPayload {
  transactionReference: string;
  amount: number;
  destinationAccountNumber: string;
  timestamp: string;
  nonce: string;
}

@Injectable()
export class SecurityInfoEncryptionService {
  private readonly _key: Buffer;

  constructor() {
    const key = config.wema.privateEncryptionKey;
    this._key = crypto
      .createHash('sha256')
      .update(Buffer.from(key, 'utf8'))
      .digest();
  }

  encryptSecurityInfo(payload: SecurityPayload): string {
    const iv = crypto.randomBytes(12);

    const cipher = crypto.createCipheriv('aes-256-gcm', this._key, iv, {
      authTagLength: 16,
    });

    const encrypted = Buffer.concat([
      cipher.update(JSON.stringify(payload), 'utf8'),
      cipher.final(),
    ]);

    const authTag = cipher.getAuthTag();

    return Buffer.concat([iv, encrypted, authTag]).toString('base64');
  }
}
