import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import {
  AuthData,
  GetAuthData,
  JwtAuthGuard,
  PinGuard,
  PinRequirement,
} from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { VersionBuildGuard } from '@crednet/utils';
import { WemaCardService } from './wema-card.service';
import { InitiateFundingDto } from './dtos/wema.fund.transactions';
import {
  VirtualCardRequestDto,
  ActivateVirtualCardDto,
} from './dtos/virtual.card.request.dto';

@Controller('wema-cards')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('WEMA Card Service')
export class WemaCardsController {
  constructor(private readonly wemaCardService: WemaCardService) {}

  @Post('fund')
  @UseGuards(PinGuard, VersionBuildGuard)
  @PinRequirement('pin')
  async initiateFunding(
    @GetAuthData() auth: AuthData,
    @Body() dto: InitiateFundingDto,
  ) {
    return this.wemaCardService.initiateFunding(dto, auth);
  }

  @Post('withdraw')
  @UseGuards(PinGuard, VersionBuildGuard)
  @PinRequirement('pin')
  async initiateDebitTransaction(
    @GetAuthData() auth: AuthData,
    @Body() dto: InitiateFundingDto,
  ) {
    return this.wemaCardService.initiateDebitTransaction(dto, auth);
  }

  @Post('request')
  @UseGuards(PinGuard, VersionBuildGuard)
  @PinRequirement('pin')
  async requestVirtualCard(
    @GetAuthData() auth: AuthData,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    @Body() dto: VirtualCardRequestDto,
  ) {
    return this.wemaCardService.initiateCardCreation(auth);
  }

  @Post('activate')
  async activateVirtualCard(
    @GetAuthData() auth: AuthData,
    @Body() dto: ActivateVirtualCardDto,
  ) {
    return this.wemaCardService.activateVirtualCard(auth, dto);
  }
}
