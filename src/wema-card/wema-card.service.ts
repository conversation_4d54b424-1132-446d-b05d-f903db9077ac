import {
  BadRequestException,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { WemaAccountRepository } from './repository/wema.account.repository';
import { AddressRepository } from './repository/address.repository';
import {
  CreateWemaAccountDto,
  VerifyWemaOtpDto,
} from './dtos/wema.account.dto';
import { AuthData } from '@crednet/authmanager';
import {
  WemaService,
  SecurityInfoEncryptionService,
  WemaCardResponse,
  WemaCardDetails,
  UserCardEncryptionService,
} from '@app/wema';
import { CreateResidentialAddressDto } from './dtos/address.dto';
import { Tier } from './entities/wema.account';
import { CardStatus, WemaCards } from './entities/wema-card.entity';
import { InitiateFundingDto } from './dtos/wema.fund.transactions';
import { WemaFundTransactionRepository } from './repository/wema.fund.transactions';
import { WemaCardCreationTransactionRepository } from './repository/wema.card.creation.transactions';
import { IsNull, MoreThan } from 'typeorm';
import { In } from 'typeorm';
import { randomUUID } from 'crypto';
import {
  Currency,
  PaymentCacheService,
  PaymentTransactionSource,
  PaymentTransactionType,
  PaymentTransactionWalletType,
  QueryTransactionDto,
  RabbitmqService,
  RequestWithdrawalDto,
  ReverseTransactionInterface,
} from '@crednet/utils';
import { Events, Exchanges, PaymentEvents, PaymentTypes } from '../utils/enums';
import {
  FundTransaction,
  TransactionStatus,
  TransactionType,
} from './entities/wema.fund.transactions';
import {
  CardCreationTransaction,
  TransactionStatus as CardCreationTransactionStatus,
} from './entities/wema.card.creation.transactions';
import * as crypto from 'crypto';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { ActivateVirtualCardDto } from './dtos/virtual.card.request.dto';
import config from '../config';
import { WemaCardRepository } from './repository/wema-card.repository';
import { WemaExternalTransactionRepository } from './repository/wema.external.transactions';

@Injectable()
export class WemaCardService {
  constructor(
    private readonly wemaAccountsRepository: WemaAccountRepository,
    private readonly wemaFundTransactionRepository: WemaFundTransactionRepository,
    private readonly wemaCardCreationTransactionRepository: WemaCardCreationTransactionRepository,
    private readonly addressRepository: AddressRepository,
    private readonly wemaCardRepository: WemaCardRepository,
    private readonly paymentCacheService: PaymentCacheService,
    private readonly rmqService: RabbitmqService,
    private readonly wemaService: WemaService,
    @InjectQueue(Events.REQUERY_FUND_WEMA_CARD)
    private readonly requeryTransactionQueue: Queue,
    @InjectQueue(Events.VALIDATE_WEMA_CARD_DETAILS)
    private readonly cardValidationQueue: Queue,
    private readonly securityInfoEncryptionService: SecurityInfoEncryptionService,
    private readonly userCardEncryptionService: UserCardEncryptionService,
    private readonly wemaExternalTransactionRepository: WemaExternalTransactionRepository,
  ) {}

  async initiateVirtualAccountCreation(
    dto: CreateWemaAccountDto,
    auth: AuthData,
  ) {
    try {
      const existingAccount = await this.wemaAccountsRepository.findOne({
        where: {
          userId: auth.id + '',
        },
      });

      if (existingAccount) {
        throw new BadRequestException('Account already exists');
      }

      const result = await this.wemaService.generatePartnershipAccount({
        phoneNumber: auth.phone_no,
        bvn: dto.bvn,
        nin: dto.nin,
        emailAddress: auth.email,
        residentialAddress: dto.residentialAddress,
        liveImageOfFace: dto.liveImageOfFace,
      });
      if (result.status !== true) {
        throw new BadRequestException(result.message);
      } else {
        const residentialAddress = this.addressRepository.create({
          ...dto.residentialAddress,
        });
        await this.addressRepository.save(residentialAddress);
        const wemaAccount = this.wemaAccountsRepository.create({
          userId: auth.id + '',
          bvn: dto.bvn,
          nin: dto.nin,
          email: auth.email,
          phoneNumber: auth.phone_no,
          residentialAddress,
          trackingId: result.data.trackingId,
        });
        await this.wemaAccountsRepository.save(wemaAccount);
        return wemaAccount;
      }
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }
  }

  async validateOtp(dto: VerifyWemaOtpDto, auth: AuthData) {
    try {
      const accountExists = await this.wemaAccountsRepository.findOne({
        where: {
          userId: auth.id + '',
        },
      });

      if (!accountExists)
        throw new BadRequestException('Account does not exist');

      const result = await this.wemaService.validateOtpAndGenerateAccount({
        otp: dto.otp,
        trackingId: accountExists.trackingId,
        phoneNumber: auth.phone_no,
      });

      if (result.status !== true) {
        throw new BadRequestException(result.message);
      } else {
        return {
          status: 'success',
          message: `Hello! ${auth.name}, your account will be created shortly. If your account is not generated, please try again in ten minutes.`,
        };
      }
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }
  }

  async resendOtp(auth: AuthData) {
    try {
      const accountExists = await this.wemaAccountsRepository.findOne({
        where: {
          userId: auth.id + '',
        },
      });

      if (!accountExists)
        throw new BadRequestException('Account does not exist');

      const result = await this.wemaService.resendOtpRequest({
        phoneNumber: auth.phone_no,
        trackingId: accountExists.trackingId,
      });
      if (result.status !== true) {
        throw new BadRequestException(result.message);
      } else {
        return {
          status: 'success',
          message: `Hi, ${auth.name}! A new Otp sent to your Phonenumber`,
        };
      }
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }
  }

  async handleAccountGeneratedEvent(data: any) {
    await this.wemaAccountsRepository.update(
      { phoneNumber: data.phoneNumber },
      {
        accountNumber: data.NUBAN,
        accountName: data.NUBANName,
      },
    );

    console.log('handleAccountGeneratedEvent', data);

    //todo: send notfication
  }

  async verifyAccountGeneration(phoneNumber: string) {
    const result =
      await this.wemaService.getPartnershipAccountDetails(phoneNumber);

    if (result.status !== true) {
      return;
    } else {
      await this.wemaAccountsRepository.update(
        { phoneNumber },
        {
          accountNumber: result.data.accountNumber,
          accountName: `${result.data.firstName} ${result.data.lastName}`,
        },
      );
    }
  }

  async requeryAccountGeneration(page: number) {
    const items = await this.wemaAccountsRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          accountNumber: IsNull(),
        },
      },
    );

    for (const item of items.items) {
      await this.verifyAccountGeneration(item.phoneNumber);
    }

    if (page < items.meta.totalPages) {
      return this.requeryAccountGeneration(++page);
    }
  }

  async handleFailedAddressVerificationEvent(data: any) {
    console.log('handleFailedAddressVerificationEvent', data);

    //todo notify user of failure so that they can update address for verification
  }

  async updateAddressForVerification(
    auth: AuthData,
    dto: CreateResidentialAddressDto,
  ) {
    try {
      const accountExists = await this.wemaAccountsRepository.findOne({
        where: {
          userId: auth.id + '',
        },
      });

      if (!accountExists)
        throw new BadRequestException('Account does not exist');

      const result = await this.wemaService.reSubmitPartnerAddress({
        accountNumber: accountExists.accountNumber,
        residentialAddress: dto,
      });

      if (result.status !== true) {
        throw new BadRequestException(result.message);
      }

      const residentialAddress = this.addressRepository.create({
        ...dto,
      });
      await this.addressRepository.save(residentialAddress);
      await this.wemaAccountsRepository.update(
        { userId: auth.id + '' },
        {
          residentialAddress,
        },
      );
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }
  }

  async handleSuccessfulAddressVerificationEvent(data: any) {
    await this.wemaAccountsRepository.update(
      { phoneNumber: data.phoneNumber },
      {
        tier: Tier.VERIFIED,
      },
    );
    //todo notify user of success
  }

  // ============= FUND TRANSACTION ============= //
  async throttleCall(userId: string, minute: number = 2) {
    // reject if successful creation txn was made at most 2 minutes ago
    const minutesAgo = new Date(Date.now() - minute * 60 * 1000); // Calculate the time 2 minutes ago and reduce by 1 hr because of timezone

    // Check funding transactions
    const pastFundTxn = await this.wemaFundTransactionRepository.findOne({
      where: {
        wemaAccounts: { userId: userId },
        createdAt: MoreThan(minutesAgo),
      },
      order: {
        createdAt: 'DESC',
      },
    });

    // Check card creation transactions
    const pastCardCreationTxn =
      await this.wemaCardCreationTransactionRepository.findOne({
        where: {
          wemaAccounts: { userId: userId },
          createdAt: MoreThan(minutesAgo),
        },
        order: {
          createdAt: 'DESC',
        },
      });

    if (pastFundTxn || pastCardCreationTxn) {
      throw new BadRequestException(
        `Transaction recently called, please wait ${minute} minutes and try again`,
      );
    }
  }

  async initiateFunding(dto: InitiateFundingDto, auth: AuthData) {
    try {
      const account = await this.wemaAccountsRepository.findOne({
        where: {
          userId: auth.id + '',
        },
      });

      if (!account) throw new BadRequestException('Account does not exist');

      await this.throttleCall(auth.id.toString());

      const reference = `cp-wema-${randomUUID()}`;

      const txn = await this.wemaFundTransactionRepository.save({
        wemaAccounts: account,
        amount: +dto.amount,
        narration: dto.narration,
        destinationAccountNumber: account.accountNumber,
        reference,
        userId: account.userId,
        type: TransactionType.FUND,
      });

      await this.paymentCacheService.savePayment({
        source: PaymentTransactionSource.CARDS_SERVICE,
        userId: account.userId,
        walletType:
          txn.walletType == PaymentTransactionWalletType.CREDPAL_CREDIT
            ? PaymentTransactionWalletType.CREDPAL_CREDIT
            : PaymentTransactionWalletType.CREDPAL_CASH,
        currency: Currency.NGN,
        reference,
        amount: +dto.amount,
        description: 'virtual naira card funding',
        returningRoutingKey: PaymentEvents.WEMA_CARD_PAYMENT_STATUS,
        meta: {
          type: PaymentTypes.FUND,
          amount: +dto.amount,
          reference,
        },
      });

      await this.wemaFundTransactionRepository.update(
        { id: txn.id },
        { paymentStatus: TransactionStatus.PROCESSING },
      );

      return txn;
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }
  }

  async finalisePayment(transaction: FundTransaction) {
    await this.wemaFundTransactionRepository.update(
      { id: transaction.id },
      { paymentStatus: TransactionStatus.SUCCESS },
    );

    const securityInfo = this.securityInfoEncryptionService.encryptSecurityInfo(
      {
        amount: transaction.amount,
        destinationAccountNumber: transaction.destinationAccountNumber,
        transactionReference: transaction.reference,
        timestamp: new Date().toISOString(),
        nonce: crypto.randomBytes(8).toString('hex'),
      },
    );

    try {
      const result = await this.wemaService.fundWallet({
        amount: transaction.amount,
        destinationAccountNumber: transaction.destinationAccountNumber,
        securityInfo,
        transactionReference: transaction.reference,
        narration: transaction.narration,
        useCustomNarration: true,
      });

      if (result.errorMessages?.length > 0 || result.errorMessage?.length > 0) {
        await this.wemaFundTransactionRepository.update(
          { id: transaction.id },
          { errors: result as any },
        );
      } else {
        await this.wemaFundTransactionRepository.update(
          { id: transaction.id },
          { status: TransactionStatus.PROCESSING },
        );
      }
    } catch (error) {
      console.error(error);
    } finally {
      this.requeryTransactionQueue.add(
        'requery_fund_wema_card',
        {
          reference: transaction.reference,
        },
        {
          lifo: false,
          attempts: 1,
          backoff: { type: 'exponential', delay: 2000 },
          jobId: randomUUID(),
          removeOnComplete: true,
          removeOnFail: false,
          delay: 10000,
        },
      );
    }
  }

  async requery(reference: string): Promise<any> {
    const transaction = await this.wemaFundTransactionRepository.findOne({
      where: { reference: reference },
      relations: ['wemaAccounts'],
    });

    if (transaction) {
      if (transaction.status == TransactionStatus.SUCCESS) {
        return;
      }
      if (transaction.type === TransactionType.FUND) {
        await this.verifyTransaction(reference);
      } else {
        await this.verifyWithdrawal(reference);
      }
      return;
    }
    // No transaction found
    console.log(`No transaction found for reference: ${reference}`);
  }

  async verifyTransaction(reference: string) {
    try {
      const data = await this.wemaService.getTransactionDetails(reference);

      switch (data?.result?.data.status.toLowerCase()) {
        case 'failed':
          this.handleError(reference, data);
          break;
        case 'success':
          this.handleSuccess(reference, data);
          break;

        default:
          break;
      }
    } catch (error) {
      console.error(error);
      this.handleError(reference, error);
    }
  }

  async handleError(reference: string, error: any) {
    const update = {
      errors: typeof error === 'string' ? { message: error } : error,
    };
    const transaction = await this.wemaFundTransactionRepository.findOne({
      where: {
        reference,
      },
      relations: ['wemaAccounts'],
    });

    update['status'] = TransactionStatus.FAILED;
    if (transaction.type === TransactionType.FUND) {
      this.refundTransaction(reference);
      this.wemaFundTransactionRepository.update({ reference }, update);
    }
    if (transaction.type === TransactionType.WITHDRAW) {
      this.wemaFundTransactionRepository.update(
        { reference },
        { ...update, paymentStatus: TransactionStatus.FAILED },
      );

      // todo notify user
    }
  }

  async handleSuccess(reference: string, data: any) {
    const txn = await this.wemaFundTransactionRepository.findOne({
      where: {
        reference,
      },
      relations: ['wemaAccounts'],
    });

    if (!txn) return;

    const fetchAccountBalance =
      await this.wemaService.getWalletAccountBalanceDetails(
        txn.wemaAccounts.accountNumber,
      );

    const account = await this.wemaAccountsRepository.findOne({
      where: {
        accountNumber: txn.wemaAccounts.accountNumber,
      },
    });

    if (!account) {
      return;
    }

    await this.wemaAccountsRepository.update(
      {
        accountNumber: txn.wemaAccounts.accountNumber,
      },
      { accountBalance: fetchAccountBalance.result.availableBalance },
    );

    if (txn.type === TransactionType.FUND) {
      await this.wemaFundTransactionRepository.update(
        { reference },
        { meta: data, status: TransactionStatus.SUCCESS },
      );
      return;
    }
    if (txn.type === TransactionType.WITHDRAW) {
      this.finaliseWithdrawal(txn);
    }
  }

  async initiateDebitTransaction(dto: InitiateFundingDto, auth: AuthData) {
    const account = await this.wemaAccountsRepository.findOne({
      where: {
        userId: auth.id + '',
      },
    });

    if (!account) throw new BadRequestException('Account does not exist');

    if (parseFloat(account.accountBalance) < dto.amount) {
      throw new ForbiddenException('Insufficient funds');
    }

    const reference = `cp-wema-wdr_` + randomUUID();

    await this.throttleCall(auth.id.toString());

    const getSourceAccount = await this.wemaService.accountNameEnquiry(
      config.wema.sourceAccountNumber,
      '035',
    );

    if (
      getSourceAccount.errorMessage !== null ||
      getSourceAccount.errorMessages?.length > 0
    ) {
      throw new BadRequestException(
        getSourceAccount.message ||
          getSourceAccount.errorMessage ||
          getSourceAccount.errorMessages,
      );
    }

    const securityInfo = this.securityInfoEncryptionService.encryptSecurityInfo(
      {
        amount: dto.amount,
        destinationAccountNumber: config.wema.sourceAccountNumber,
        transactionReference: reference,
        timestamp: new Date().toISOString(),
        nonce: crypto.randomBytes(8).toString('hex'),
      },
    );

    const initiateWithdrawCall = await this.wemaService.debitWallet({
      amount: dto.amount,
      destinationAccountNumber: config.wema.sourceAccountNumber,
      destinationBankName: 'WEMA BANK',
      destinationBankCode: '035',
      destinationAccountName: getSourceAccount.result.accountName,
      sourceAccountNumber: account.accountNumber,
      securityInfo,
      transactionReference: reference,
      narration: dto.narration,
      useCustomNarration: true,
    });

    if (
      initiateWithdrawCall.errorMessage !== null ||
      initiateWithdrawCall.errorMessages?.length > 0
    ) {
      throw new BadRequestException(
        initiateWithdrawCall.message ||
          initiateWithdrawCall.errorMessage ||
          initiateWithdrawCall.errorMessages,
      );
    }

    const txn = await this.wemaFundTransactionRepository.save({
      wemaAccounts: account,
      amount: +dto.amount,
      narration: dto.narration,
      destinationAccountNumber: 'SOURCE',
      reference,
      userId: account.userId,
      type: TransactionType.WITHDRAW,
      status: TransactionStatus.PROCESSING,
    });

    return txn;
  }

  async verifyWithdrawal(reference: string) {
    try {
      const data = await this.wemaService.confirmClientStatus(reference);

      switch (data?.result?.data.status.toLowerCase()) {
        case 'failed':
          this.handleError(reference, data);
          break;
        case 'success':
          this.handleSuccess(reference, data);
          break;

        default:
          break;
      }
    } catch (error) {
      console.error(error);
      this.handleError(reference, error);
    }
  }

  async finaliseWithdrawal(transaction: FundTransaction) {
    await this.wemaFundTransactionRepository.update(
      { id: transaction.id },
      {
        status: TransactionStatus.SUCCESS,
        paymentStatus: TransactionStatus.PROCESSING,
      },
    );

    const fetchAccountBalance =
      await this.wemaService.getWalletAccountBalanceDetails(
        transaction.wemaAccounts.accountNumber,
      );

    const account = await this.wemaAccountsRepository.findOne({
      where: {
        accountNumber: transaction.wemaAccounts.accountNumber,
      },
    });

    if (!account) {
      return;
    }

    await this.wemaAccountsRepository.update(
      {
        accountNumber: transaction.wemaAccounts.accountNumber,
      },
      { accountBalance: fetchAccountBalance.result.availableBalance },
    );

    await this.rmqService.send(Exchanges.PAYMENT, {
      key: PaymentEvents.TOP_UP,
      data: {
        transaction: {
          source: PaymentTransactionSource.CARDS_SERVICE,
          userId: transaction.wemaAccounts.userId,
          currency: Currency.NGN,
          amount: transaction.amount,
          type: PaymentTransactionType.CREDIT,
          description: 'Withdraw from wema virtual card',
          meta: transaction.meta,
          reference: transaction.reference,
          returningRoutingKey: PaymentEvents.WEMA_CARD_PAYMENT_STATUS,
        },
        returningRoutingKey: PaymentEvents.WEMA_CARD_PAYMENT_STATUS,
      } as RequestWithdrawalDto,
    });
  }

  async requeryProcessing(page: number) {
    console.log('running job:: requeryProcesing ', page);
    const fundingItems = await this.wemaFundTransactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          type: TransactionType.FUND,
          status: TransactionStatus.PROCESSING,
          paymentStatus: TransactionStatus.SUCCESS,
        },
      },
    );

    const withdrawalItems = await this.wemaFundTransactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          type: TransactionType.WITHDRAW,
          status: TransactionStatus.PROCESSING,
          paymentStatus: TransactionStatus.PENDING,
        },
      },
    );

    const items = {
      items: [...fundingItems.items, ...withdrawalItems.items],
      meta: {
        totalPages: Math.max(
          fundingItems.meta.totalPages,
          withdrawalItems.meta.totalPages,
        ),
      },
    };

    for (const item of items.items) {
      try {
        await this.requery(item.reference);
      } catch (error) {
        console.error(error);
      }
    }

    if (page < items.meta.totalPages) {
      return this.requeryProcessing(++page);
    }
  }

  async handleRefund(page: number) {
    console.log('running job:: handleRefund ', page);
    const items = await this.wemaFundTransactionRepository.findMany(
      {
        page,
        limit: 100,
      },
      {
        where: {
          status: TransactionStatus.FAILED,
          paymentStatus: TransactionStatus.SUCCESS,
          isRefunded: false,
        },
      },
    );

    for (const item of items.items) {
      console.log('running job:: handleRefund ', item.reference);

      this.refundTransaction(item.reference);
    }

    if (page < items.meta.totalPages) {
      return this.handleRefund(++page);
    }
  }

  async refundTransaction(reference: string) {
    this.rmqService.send(Exchanges.PAYMENT, {
      key: PaymentEvents.REVERSE_TRANSACTION,
      data: {
        source: PaymentTransactionSource.CARDS_SERVICE,
        reference,
        reason: 'Transaction failed from provider',
        returningRoutingKey: PaymentEvents.WEMA_CARD_PAYMENT_STATUS,
      } as ReverseTransactionInterface,
    });
  }

  async reprocessPendingProcessing(page: number) {
    console.log('running job:: reprocessPendingProcessing ', page);
    const fundingItems = await this.wemaFundTransactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          status: TransactionStatus.PENDING,
          paymentStatus: TransactionStatus.SUCCESS,
        },
      },
    );

    const cardCreationItems =
      await this.wemaCardCreationTransactionRepository.findMany(
        {
          page,
          limit: 10,
        },
        {
          where: {
            status: CardCreationTransactionStatus.PENDING,
            paymentStatus: CardCreationTransactionStatus.SUCCESS,
          },
          relations: ['wemaAccounts', 'wemaAccounts.residentialAddress'],
        },
      );

    for (const item of fundingItems.items) {
      await this.finalisePayment(item);
    }

    for (const item of cardCreationItems.items) {
      await this.finaliseCardCreation(item);
    }

    const totalPages = Math.max(
      fundingItems.meta.totalPages,
      cardCreationItems.meta.totalPages,
    );
    if (page < totalPages) {
      return this.reprocessPendingProcessing(++page);
    }
  }

  async requeryPendingPayment(page: number) {
    console.log('running job:: requeryPendingPayment ', page);
    const fundingItems = await this.wemaFundTransactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          paymentStatus: In([
            TransactionStatus.PENDING,
            TransactionStatus.PROCESSING,
          ]),
          type: TransactionType.FUND,
        },
      },
    );

    const withdrawalItems = await this.wemaFundTransactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          paymentStatus: In([
            TransactionStatus.PENDING,
            TransactionStatus.PROCESSING,
          ]),
          status: TransactionStatus.SUCCESS,
          type: TransactionType.WITHDRAW,
        },
      },
    );

    console.log('requeryPendingPayment ', withdrawalItems.items.length);

    const items = {
      items: [...fundingItems.items, ...withdrawalItems.items],
      meta: {
        totalPages: Math.max(
          fundingItems.meta.totalPages,
          withdrawalItems.meta.totalPages,
        ),
      },
    };

    const cardCreationItems =
      await this.wemaCardCreationTransactionRepository.findMany(
        {
          page,
          limit: 10,
        },
        {
          where: {
            paymentStatus: In([
              CardCreationTransactionStatus.PENDING,
              CardCreationTransactionStatus.PROCESSING,
            ]),
          },
        },
      );

    for (const item of items.items) {
      this.rmqService.send(Exchanges.PAYMENT, {
        key: PaymentEvents.QUERY_TRANSACTION,
        data: {
          returningRoutingKey: PaymentEvents.WEMA_CARD_PAYMENT_STATUS,
          reference: item.reference,
        } as QueryTransactionDto,
      });
    }

    for (const item of cardCreationItems.items) {
      this.rmqService.send(Exchanges.PAYMENT, {
        key: PaymentEvents.QUERY_TRANSACTION,
        data: {
          returningRoutingKey: PaymentEvents.WEMA_CARD_CREATION_PAYMENT_STATUS,
          reference: item.reference,
        } as QueryTransactionDto,
      });
    }

    const totalPages = Math.max(
      fundingItems.meta.totalPages,
      withdrawalItems.meta.totalPages,
      cardCreationItems.meta.totalPages,
    );
    if (page < totalPages) {
      return this.requeryPendingPayment(++page);
    }
  }

  async handleFundTransactionWebhook(data: any) {
    if (data.Status.toLowerCase() === 'success') {
      this.handleSuccess(data.TransactionReference, data);
    } else {
      this.handleError(data.TransactionReference, data);
    }
  }

  async handleTransactionNotificationWebhook(data: any) {
    // this.handleSuccess(data.transactionReference, data);
    const fetchAccountBalance =
      await this.wemaService.getWalletAccountBalanceDetails(data.accountNumber);

    const account = await this.wemaAccountsRepository.findOne({
      where: {
        accountNumber: data.accountNumber,
      },
    });

    if (!account) {
      return;
    }

    await this.wemaAccountsRepository.update(
      {
        accountNumber: data.accountNumber,
      },
      { accountBalance: fetchAccountBalance.result.availableBalance },
    );

    await this.wemaExternalTransactionRepository.save({
      wemaAccounts: account,
      transactionType: data.transactionType,
      amount: data.amount,
      narration: data.narration,
    });

    // todo add notification for this
  }

  //================= VIRTUAL CARD GENERATION ====================//

  /**
   * Initiates card creation with payment integration
   * @param auth - Authentication data
   * @param dto - Card creation request DTO
   * @returns Card creation transaction record
   */
  async initiateCardCreation(auth: AuthData) {
    try {
      const account = await this.wemaAccountsRepository.findOne({
        where: {
          userId: auth.id + '',
        },
      });

      if (!account) throw new BadRequestException('Account does not exist');

      await this.throttleCall(auth.id.toString());

      const reference = `cp-wema-card-${randomUUID()}`;

      const txn = await this.wemaCardCreationTransactionRepository.save({
        wemaAccounts: account,
        amount: 1200,
        narration: 'Virtual card creation',
        reference,
        userId: account.userId,
      });

      await this.paymentCacheService.savePayment({
        source: PaymentTransactionSource.CARDS_SERVICE,
        userId: account.userId,
        walletType:
          txn.walletType == PaymentTransactionWalletType.CREDPAL_CREDIT
            ? PaymentTransactionWalletType.CREDPAL_CREDIT
            : PaymentTransactionWalletType.CREDPAL_CASH,
        currency: Currency.NGN,
        reference,
        amount: 1200,
        description: 'virtual naira card creation',
        returningRoutingKey: PaymentEvents.WEMA_CARD_CREATION_PAYMENT_STATUS,
        meta: {
          type: PaymentTypes.CREATE,
          amount: 1200,
          reference,
        },
      });

      await this.wemaCardCreationTransactionRepository.update(
        { id: txn.id },
        { paymentStatus: CardCreationTransactionStatus.PROCESSING },
      );

      return txn;
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }
  }

  /**
   * Finalizes card creation after successful payment
   * @param transaction - Card creation transaction record
   */
  async finaliseCardCreation(transaction: CardCreationTransaction) {
    await this.wemaCardCreationTransactionRepository.update(
      { id: transaction.id },
      { paymentStatus: CardCreationTransactionStatus.SUCCESS },
    );

    try {
      const result = await this.wemaService.createVirtualCard({
        emailaddress: transaction.wemaAccounts.email,
        phoneNumber: transaction.wemaAccounts.phoneNumber,
        // amount: transaction.amount,
        accountNo: transaction.wemaAccounts.accountNumber,
        customerAddress:
          transaction.wemaAccounts.residentialAddress.fullAddress,
        cardKey: config.wema.cardKey,
        currency: Currency.NGN,
        CustomerState: transaction.wemaAccounts.residentialAddress.state,
      });

      console.log('finaliseCardCreation', result);

      if (result.status !== true) {
        await this.wemaCardCreationTransactionRepository.update(
          { id: transaction.id },
          {
            status: CardCreationTransactionStatus.FAILED,
            errors: result as any,
          },
        );

        if (transaction.isRefunded === false) {
          await this.rmqService.send(Exchanges.PAYMENT, {
            key: PaymentEvents.REVERSE_TRANSACTION,
            data: {
              reference: transaction.reference,
              reason: 'Card creation failed',
            } as ReverseTransactionInterface,
          });

          await this.wemaCardCreationTransactionRepository.update(
            { id: transaction.id },
            { isRefunded: true },
          );
        }

        throw new BadRequestException(result.message);
      } else {
        // Save card record with CMP number
        const card = await this.wemaCardRepository.save({
          userId: transaction.userId,
          cmpNumber: result.data,
          wemaAccounts: transaction.wemaAccounts,
        });

        // Update transaction with CMP number and success status
        await this.wemaCardCreationTransactionRepository.update(
          { id: transaction.id },
          {
            status: CardCreationTransactionStatus.SUCCESS,
            cmpNumber: result.data,
          },
        );

        await this.scheduleCardValidation(card.id, result.data);

        console.log(
          `Card creation completed and validation scheduled for card: ${result.data}`,
        );

        return card;
      }
    } catch (error) {
      console.error('Error in finaliseCardCreation:', error);

      await this.wemaCardCreationTransactionRepository.update(
        { id: transaction.id },
        {
          status: CardCreationTransactionStatus.FAILED,
          errors: error as any,
        },
      );

      if (transaction.isRefunded === false) {
        await this.rmqService.send(Exchanges.PAYMENT, {
          key: PaymentEvents.REVERSE_TRANSACTION,
          data: {
            reference: transaction.reference,
            reason: 'Card creation failed due to system error',
          } as ReverseTransactionInterface,
        });

        await this.wemaCardCreationTransactionRepository.update(
          { id: transaction.id },
          { isRefunded: true },
        );
      }

      throw error;
    } finally {
      // Add requery job for card creation transaction
      this.requeryTransactionQueue.add(
        'requery_card_creation_wema_card',
        {
          reference: transaction.reference,
        },
        {
          lifo: false,
          attempts: 1,
          backoff: { type: 'exponential', delay: 2000 },
          jobId: randomUUID(),
          removeOnComplete: true,
          removeOnFail: true,
          delay: 10000,
        },
      );
    }
  }

  async handleCardCreationWebhook(data: WemaCardResponse) {
    console.log('handleCardCreationWebhook', data);

    const card = await this.wemaCardRepository.findOne({
      where: {
        cmpNumber: data.data,
      },
      relations: ['wemaAccounts'],
    });

    if (!card) {
      return;
    }

    await this.encryptCardDetails(card);
  }

  async reCheckCardDetails(page: number) {
    const cards = await this.wemaCardRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          encryptedCardDetails: IsNull(),
        },
      },
    );

    for (const card of cards.items) {
      await this.encryptCardDetails(card);
    }

    if (page < cards.meta.totalPages) {
      return this.reCheckCardDetails(++page);
    }
  }

  async encryptCardDetails(card: WemaCards) {
    try {
      const cardDetails: WemaCardDetails =
        await this.wemaService.getVirtualCardDetails(
          card.wemaAccounts.accountNumber,
        );

      console.log(cardDetails);

      const encryptedCardDetails =
        this.userCardEncryptionService.encryptCardDetails(
          cardDetails,
          card.wemaAccounts.userId,
          card.wemaAccounts.id,
        );

      await this.wemaCardRepository.update(
        { id: card.id },
        { encryptedCardDetails },
      );

      console.log(
        `Card details encrypted and saved for card: ${card.cmpNumber}`,
      );
    } catch (error) {
      console.error('Error encrypting card details:', error);
      throw error;
    }
  }

  /**
   * Schedules a card validation task to run after a specified delay
   * @param cardId - The card ID to validate
   * @param cmpNumber - The CMP number for logging
   * @param delayMs - Delay in milliseconds before running the task
   * @param retryCount - Current retry count (optional)
   */
  async scheduleCardValidation(
    cardId: string,
    cmpNumber: string,
    delayMs: number = 10 * 60 * 1000,
    retryCount: number = 0,
  ) {
    try {
      await this.cardValidationQueue.add(
        Events.VALIDATE_WEMA_CARD_DETAILS,
        {
          cardId,
          cmpNumber,
          retryCount,
        },
        {
          delay: delayMs,
          attempts: 1,
          removeOnComplete: 10,
          removeOnFail: 50,
        },
      );

      console.log(
        `Scheduled card validation for card: ${cmpNumber} with ${delayMs}ms delay, retry: ${retryCount}`,
      );
    } catch (error) {
      console.error('Error scheduling card validation:', error);
      throw error;
    }
  }

  /**
   * Gets a card by ID with relations
   * @param cardId - The card ID
   * @returns The card entity or null
   */
  async getCardById(cardId: string): Promise<WemaCards | null> {
    return this.wemaCardRepository.findOne({
      where: { id: cardId },
      relations: ['wemaAccounts'],
    });
  }

  /**
   * Validates and encrypts card details, checking for CVV "000" and handling errors
   * @param card - The card entity to validate
   * @returns true if validation successful and card details saved, false if CVV is "000"
   * @throws Error if decryption/validation fails
   */
  async validateAndEncryptCardDetails(card: WemaCards): Promise<boolean> {
    try {
      const cardDetails: WemaCardDetails =
        await this.wemaService.getVirtualCardDetails(
          card.wemaAccounts.accountNumber,
        );

      console.log('Retrieved card details for validation:', {
        cmpNumber: card.cmpNumber,
        cvv: cardDetails.cvv,
        hasCardDetails: !!cardDetails,
      });

      if (cardDetails.cvv === '000') {
        console.log(
          `Card details not ready for card: ${card.cmpNumber}, CVV is "000"`,
        );
        return false;
      }

      const encryptedCardDetails =
        this.userCardEncryptionService.encryptCardDetails(
          cardDetails,
          card.wemaAccounts.userId,
          card.wemaAccounts.id,
        );

      await this.wemaCardRepository.update(
        { id: card.id },
        { encryptedCardDetails },
      );

      console.log(
        `Card details successfully validated and encrypted for card: ${card.cmpNumber}`,
      );
      return true;
    } catch (error) {
      console.error(
        `Error validating card details for card: ${card.cmpNumber}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Activates a virtual card using the provided PIN
   * @param auth - Authentication data
   * @param dto - Activation DTO containing the PIN
   * @returns Success response
   */
  async activateVirtualCard(auth: AuthData, dto: ActivateVirtualCardDto) {
    try {
      const card = await this.wemaCardRepository.findOne({
        where: {
          userId: auth.id + '',
        },
        relations: ['wemaAccounts'],
      });

      if (!card) {
        throw new BadRequestException('No card found for this user');
      }

      if (!card.encryptedCardDetails) {
        throw new BadRequestException(
          'Card details not available. Please contact support.',
        );
      }

      const cardDetails =
        this.userCardEncryptionService.decryptCardDetails<WemaCardDetails>(
          card.encryptedCardDetails,
          card.wemaAccounts.userId,
          card.wemaAccounts.id,
        );

      // const expire =
      //   cardDetails.expiry.slice(0, 2) + cardDetails.expiry.slice(2, 4);

      // console.log(expire);

      const activationResult = await this.wemaService.activateCard(
        card.wemaAccounts.accountNumber,
        dto.pin,
        cardDetails.expiry,
        card.wemaAccounts.email,
        cardDetails.pan,
      );

      if (activationResult.successful) {
        await this.wemaCardRepository.update(
          { id: card.id },
          { status: CardStatus.ACTIVE },
        );

        return {
          status: 'success',
          message: 'Card activated successfully',
          data: {
            cardId: card.id,
            status: CardStatus.ACTIVE,
          },
        };
      } else {
        throw new BadRequestException(
          activationResult.message || 'Card activation failed',
        );
      }
    } catch (error) {
      console.error('Error activating virtual card:', error);
      throw new BadRequestException(
        error || 'Failed to activate card. Please try again.',
      );
    }
  }
}
