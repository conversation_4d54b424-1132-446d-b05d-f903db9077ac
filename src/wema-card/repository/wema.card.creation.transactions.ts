import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { CardCreationTransaction } from '../entities/wema.card.creation.transactions';

@Injectable()
export class WemaCardCreationTransactionRepository extends TypeOrmRepository<CardCreationTransaction> {
  constructor(private readonly dataSource: DataSource) {
    super(CardCreationTransaction, dataSource.createEntityManager());
  }
}
