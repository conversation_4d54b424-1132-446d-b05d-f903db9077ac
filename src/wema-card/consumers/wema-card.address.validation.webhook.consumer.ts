import { RabbitmqService } from '@crednet/utils';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { WemaCardService } from '../wema-card.service';
import { Exchanges, WemaWebhookEvent } from '../../utils/enums';

@Injectable()
export class WemaCardAddressValidationWebhookConsumer implements OnModuleInit {
  constructor(
    private readonly rabbitmqService: RabbitmqService,
    private readonly wemaCardService: WemaCardService,
  ) {}

  onModuleInit() {
    this.rabbitmqService.subscribe(
      `${Exchanges.WEBHOOK}.wema.${WemaWebhookEvent.ADDRESS_VALIDATION}`,
      async ({ data, ack }) => {
        try {
          if (data.title.includes('Successfully')) {
            await this.wemaCardService.handleSuccessfulAddressVerificationEvent(
              data.data,
            );
            ack();
          } else {
            await this.wemaCardService.handleFailedAddressVerificationEvent(
              data.data,
            );
            ack();
          }
        } catch (error) {
          console.error(error);
          throw error;
        }
      },
    );
  }
}
