import { RabbitmqService } from '@crednet/utils';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { WemaCardService } from '../wema-card.service';
import { Exchanges, WemaWebhookEvent } from '../../utils/enums';

@Injectable()
export class WemaCardTransactionWebhookConsumer implements OnModuleInit {
  constructor(
    private readonly rabbitmqService: RabbitmqService,
    private readonly wemaCardService: WemaCardService,
  ) {}

  async onModuleInit() {
    this.rabbitmqService.subscribe(
      `${Exchanges.WEBHOOK}.wema.${WemaWebhookEvent.TRANSACTION_NOTIFICATION}`,
      async ({ data, ack }) => {
        try {
          console.log(999);

          await this.wemaCardService.handleTransactionNotificationWebhook(data);
          ack();
        } catch (error) {
          console.error(error);
          throw error;
        }
      },
    );
  }
}
