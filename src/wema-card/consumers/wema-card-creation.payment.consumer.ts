import { Injectable, OnModuleInit } from '@nestjs/common';
import { WemaCardService } from '../wema-card.service';
import { WemaCardCreationTransactionRepository } from '../repository/wema.card.creation.transactions';
import {
  PaymentTransaction,
  PaymentTransactionStatus,
  PaymentTransactionWalletType,
  QueryTransactionResponseDto,
  RabbitmqService,
} from '@crednet/utils';
import { Exchanges, PaymentEvents } from '../../utils/enums';
import {
  CardCreationTransaction,
  TransactionStatus,
} from '../entities/wema.card.creation.transactions';

@Injectable()
export class WemaCardCreationPaymentConsumer implements OnModuleInit {
  constructor(
    private readonly rmqService: RabbitmqService,
    private readonly wemaCardService: WemaCardService,
    private readonly wemaCardCreationTransactionRepository: WemaCardCreationTransactionRepository,
  ) {}

  onModuleInit() {
    this.rmqService.subscribe(
      `${Exchanges.PAYMENT}.${PaymentEvents.WEMA_CARD_CREATION_PAYMENT_STATUS}`,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      async ({ data, message, client, ack, reject }) => {
        try {
          if (data?.status) {
            await this.handlePaymentStatus(data, ack, reject);
          }
        } catch (error) {
          //force the error to be thrown so that rabbitmq can nack it in the rabbitmq service
          throw error;
        }
      },
    );
  }

  async handlePaymentStatus(
    payload: QueryTransactionResponseDto,
    ack: () => void,
    reject: () => void,
  ) {
    try {
      const { reference, status, transaction } = payload;
      let data = await this.wemaCardCreationTransactionRepository.findOne({
        where: { reference },
        relations: ['wemaAccounts', 'wemaAccounts.residentialAddress'],
      });

      if (!data) {
        data = await this.wemaCardCreationTransactionRepository.findOne({
          where: { reference: reference },
          relations: ['wemaAccounts', 'wemaAccounts.residentialAddress'],
        });

        if (!data) {
          return;
        }
      }

      if (
        (data?.paymentStatus != TransactionStatus.SUCCESS &&
          data?.paymentStatus != TransactionStatus.FAILED) ||
        (status == PaymentTransactionStatus.REVERSED && !data?.isRefunded)
      ) {
        switch (status) {
          case PaymentTransactionStatus.SUCCESSFUL:
            await this.handleSuccess(data, transaction);
            break;

          case PaymentTransactionStatus.FAILED:
            await this.handleFailed(data, transaction, payload.error);
            break;

          case PaymentTransactionStatus.REVERSED:
            await this.handleRefunded(data);
            break;

          case PaymentTransactionStatus.NOT_FOUND:
            if (
              data.paymentStatus == TransactionStatus.PENDING ||
              data.paymentStatus == TransactionStatus.PROCESSING
            ) {
              await this.handleNotFound(data, transaction);
            }
            break;

          default:
            break;
        }
      }
      ack();
    } catch (e) {
      console.log(e);
      if (String(e?.message).includes('already exist')) {
        ack();
        return;
      }

      reject();
    }
  }

  private async handleSuccess(
    data: CardCreationTransaction,
    transaction: PaymentTransaction,
  ) {
    const update = { paymentStatus: TransactionStatus.SUCCESS };
    if (transaction.walletType) {
      update['walletType'] = transaction.walletType;
    }
    await this.wemaCardCreationTransactionRepository.update(
      { id: data.id },
      update,
    );

    await this.wemaCardService.finaliseCardCreation(data);
  }

  private async handleFailed(
    data: CardCreationTransaction,
    transaction: PaymentTransaction,
    error: object,
  ) {
    const update = {
      paymentStatus: TransactionStatus.FAILED,
      status: TransactionStatus.FAILED,
      errors: error,
    };
    if (data.walletType) {
      update['walletType'] = transaction.walletType;
    }
    await this.wemaCardCreationTransactionRepository.update(
      { id: data.id },
      update,
    );

    // Todo: Notification can be added here similar to funding flow.
  }

  private async handleRefunded(data: CardCreationTransaction) {
    const update = { isRefunded: true };
    if (
      data.status == TransactionStatus.PENDING ||
      data.status == TransactionStatus.PROCESSING
    ) {
      update['status'] = TransactionStatus.FAILED;
    }

    if (
      data.paymentStatus == TransactionStatus.PROCESSING ||
      data.paymentStatus == TransactionStatus.PENDING
    ) {
      update['paymentStatus'] = TransactionStatus.FAILED;
    }
    await this.wemaCardCreationTransactionRepository.update(
      { id: data.id },
      update,
    );
  }

  private async handleNotFound(
    data: CardCreationTransaction,
    transaction: PaymentTransaction,
  ) {
    const paymentWalletResponse = { ...data.meta?.paymentWalletResponse };

    if (!paymentWalletResponse[transaction.walletType]) {
      paymentWalletResponse[transaction.walletType] = transaction.status;
    }
    const meta = { ...data.meta, paymentWalletResponse };

    await this.wemaCardCreationTransactionRepository.update(
      { id: data.id },
      { meta },
    );

    if (
      paymentWalletResponse[PaymentTransactionWalletType.CREDPAL_CREDIT] ==
        PaymentTransactionStatus.NOT_FOUND &&
      paymentWalletResponse[PaymentTransactionWalletType.CREDPAL_CASH] ==
        PaymentTransactionStatus.NOT_FOUND
    ) {
      const billAge = Date.now() - data.createdAt.getTime();
      const tenMinutesInMs = 10 * 60 * 1000; // 10 minutes in milliseconds

      if (billAge >= tenMinutesInMs) {
        await this.wemaCardCreationTransactionRepository.update(
          { id: data.id },
          {
            meta,
            status: TransactionStatus.ABANDONED,
            paymentStatus: TransactionStatus.ABANDONED,
          },
        );
      }
    }
  }
}
