import { ApiProperty } from '@nestjs/swagger';
import { IsString, Matches, IsNotEmpty } from 'class-validator';

export class VirtualCardRequestDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Matches(/^[0-9]{4}$/, {
    message: 'Pin must be exactly 4 digits',
  })
  pin: string;
}

export class ActivateVirtualCardDto {
  @ApiProperty({
    description: 'A 4-digit PIN for card activation',
    example: '1234',
    pattern: '^[0-9]{4}$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[0-9]{4}$/, {
    message: 'Pin must be exactly 4 digits',
  })
  pin: string;
}
