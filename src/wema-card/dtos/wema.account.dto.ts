import { IsString, IsNotEmpty, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateResidentialAddressDto } from './address.dto';
import { ApiProperty } from '@nestjs/swagger';

export class CreateWemaAccountDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  bvn: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  nin: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  liveImageOfFace: string;

  @ApiProperty()
  @ValidateNested()
  @Type(() => CreateResidentialAddressDto)
  residentialAddress: CreateResidentialAddressDto;
}

export class VerifyWemaOtpDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  otp: string;
}
