import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Events } from 'src/utils/enums';
import { Logger } from '@nestjs/common';
import { WemaCardService } from '../wema-card.service';
import { Job } from 'bullmq';

export interface CardValidationJobData {
  cardId: string;
  cmpNumber: string;
  retryCount?: number;
}

@Processor(Events.VALIDATE_WEMA_CARD_DETAILS)
export class CardValidationProcessor extends WorkerHost {
  private readonly logger = new Logger(CardValidationProcessor.name);
  private readonly MAX_RETRY_COUNT = 50;

  constructor(private readonly wemaCardService: WemaCardService) {
    super();
  }

  async process(job: Job<CardValidationJobData>) {
    try {
      this.logger.debug(
        `Processing card validation job for card: ${job.data.cardId}, CMP: ${job.data.cmpNumber}, retry: ${job.data.retryCount || 0}`,
      );

      const { cardId, cmpNumber, retryCount = 0 } = job.data;

      const card = await this.wemaCardService.getCardById(cardId);
      if (!card) {
        this.logger.error(`Card not found with ID: ${cardId}`);
        return;
      }

      if (card.encryptedCardDetails) {
        this.logger.log(
          `Card details already encrypted for card: ${cmpNumber}`,
        );
        return;
      }

      try {
        const isValid =
          await this.wemaCardService.validateAndEncryptCardDetails(card);

        if (isValid) {
          this.logger.log(
            `Card details successfully validated and encrypted for card: ${cmpNumber}`,
          );
          return;
        }

        if (retryCount < this.MAX_RETRY_COUNT) {
          this.logger.log(
            `Card validation failed for card: ${cmpNumber}. Scheduling retry ${retryCount + 1}/${this.MAX_RETRY_COUNT} in 2 minutes`,
          );
          await this.scheduleRetry(job.data, retryCount + 1);
        } else {
          this.logger.error(
            `Card validation failed after ${this.MAX_RETRY_COUNT} retries for card: ${cmpNumber}. Giving up.`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Error during card validation for card: ${cmpNumber}, retry: ${retryCount}. Error: ${error.message}`,
        );

        if (retryCount < this.MAX_RETRY_COUNT) {
          this.logger.log(
            `Scheduling retry ${retryCount + 1}/${this.MAX_RETRY_COUNT} in 2 minutes due to error`,
          );
          await this.scheduleRetry(job.data, retryCount + 1);
        } else {
          this.logger.error(
            `Card validation failed after ${this.MAX_RETRY_COUNT} retries for card: ${cmpNumber}. Giving up.`,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        `Critical error processing card validation job: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async scheduleRetry(
    jobData: CardValidationJobData,
    newRetryCount: number,
  ) {
    const { cardId, cmpNumber } = jobData;

    await this.wemaCardService.scheduleCardValidation(
      cardId,
      cmpNumber,
      2 * 60 * 1000,
      newRetryCount,
    );
  }
}
