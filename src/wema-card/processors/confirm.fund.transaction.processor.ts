import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Events } from '../../utils/enums';
import { Logger } from '@nestjs/common';
import { WemaCardService } from '../wema-card.service';
import { Job } from 'bullmq';

@Processor(Events.REQUERY_FUND_WEMA_CARD)
export class RequeryFundWemaCardProcessor extends WorkerHost {
  private readonly logger = new Logger(RequeryFundWemaCardProcessor.name);

  constructor(private readonly wemaCardService: WemaCardService) {
    super();
  }

  async process(job: Job) {
    try {
      this.logger.debug(
        `Processing requery job for ${Events.REQUERY_FUND_WEMA_CARD}, `,
        job.asJSON(),
      );
      const data: { reference: string } = job.data;

      // The requery method now handles both funding and card creation transactions
      await this.wemaCardService.requery(data.reference);
    } catch (error) {
      this.logger.error(
        `Error processing requery job: ${error.message}`,
        error.stack,
      );
      throw error; // Rethrow to trigger backoff retry
    }
  }
}
