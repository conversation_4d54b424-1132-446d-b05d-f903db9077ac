import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { WemaAccounts } from './wema.account';
import { PaymentTransactionWalletType } from '@crednet/utils';

export enum TransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  FAILED = 'failed',
  ABANDONED = 'abandoned',
  EXTERNAL = 'external',
}

@Entity()
export class CardCreationTransaction extends BaseEntity {
  @Column({ nullable: true })
  @Index('reference')
  reference: string;

  @Index('userId')
  @Column({
    nullable: false,
  })
  userId: string;

  @Column({
    nullable: false,
    type: 'decimal',
    precision: 10,
    scale: 2,
  })
  amount: number;

  @Column({
    nullable: false,
  })
  narration: string;

  @Column({
    type: 'enum',
    enum: [
      PaymentTransactionWalletType.CREDPAL_CASH,
      PaymentTransactionWalletType.CREDPAL_CREDIT,
    ],
    nullable: true,
  })
  walletType: PaymentTransactionWalletType;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
  })
  status: TransactionStatus;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
  })
  paymentStatus: TransactionStatus;

  @ManyToOne(() => WemaAccounts, (wemaAccount) => wemaAccount.id)
  wemaAccounts: WemaAccounts;

  @Column({ type: 'json', nullable: true })
  meta: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  errors: Record<string, any>;

  @Column({ default: false })
  isRefunded: boolean;

  @Column({ nullable: true })
  cmpNumber: string; // Store the CMP number returned from card creation
}
