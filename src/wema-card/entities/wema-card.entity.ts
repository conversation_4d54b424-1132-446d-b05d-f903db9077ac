import { Column, Entity, Index, ManyToOne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { WemaAccounts } from './wema.account';

export enum CardStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  TERMINATED = 'terminated',
  BLOCKED = 'blocked',
}

@Entity({
  name: 'wema-cards',
})
export class WemaCards extends BaseEntity {
  @Column()
  @Index()
  userId: string;

  @Column()
  cmpNumber: string;

  @Column({ nullable: true, type: 'longtext' })
  encryptedCardDetails: string;

  @Column({
    type: 'enum',
    enum: CardStatus,
    default: CardStatus.INACTIVE,
  })
  status: CardStatus;

  @ManyToOne(() => WemaAccounts, { eager: true })
  @JoinColumn({ name: 'wema_account_id' })
  wemaAccounts: WemaAccounts;
}
