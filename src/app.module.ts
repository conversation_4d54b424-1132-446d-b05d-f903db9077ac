import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { CardModule } from './card/card.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { typeOrmConfig } from './config/data-source';
import { WebhookModule } from './webhook/webhook.module';
import { CronModule } from './cron/cron.module';
import { ScheduleModule } from '@nestjs/schedule';
import { CacheManagerModule } from '@crednet/authmanager';
import { ConfigurationsModule } from './configurations/configurations.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { LogsModule } from './logs1/logs.module';
import config from './config';
import {
  BrideCardEvents,
  Exchanges,
  MidenWebhookEvent,
  PaymentEvents,
  WemaWebhookEvent,
} from './utils/enums';
import { RabbitmqModule, RedisModule } from '@crednet/utils';
import { BullModule } from '@nestjs/bullmq';
import { AdminModule } from './admin/admin.module';
import { NotificationModule } from './notification/notification.module';
import { WemaCardModule } from './wema-card/wema-card.module';
@Module({
  imports: [
    TypeOrmModule.forRoot(typeOrmConfig),
    ScheduleModule.forRoot(),
    CacheManagerModule.register(),
    RedisModule.forRoot({
      redis: {
        url: config.redis.url,
      },
    }),
    CardModule,
    WebhookModule,
    ...(config.isCronEnabled == 'true' ? [CronModule] : []),
    RabbitmqModule.register({
      host: config.rabbitMq.brockers[0],
      queueName: 'cards.queue.dev',
      prefetchCount: 10,
      showLog: true,
      deadLetterQueueInterval: 30000,
      // consumeDeadLetterQueue: config.isCronEnabled == 'true',
      consumeDeadLetterQueue: false,
      producer: {
        name: config.rabbitMq.exchange,
        durable: true,
      },
      subscriptions: [
        `${Exchanges.PAYMENT}.${PaymentEvents.CARD_PAYMENT_STATUS}`,
        `${Exchanges.PAYMENT}.${PaymentEvents.WEMA_CARD_PAYMENT_STATUS}`,
        `${Exchanges.PAYMENT}.${PaymentEvents.WEMA_CARD_CREATION_PAYMENT_STATUS}`,
        ...Object.values(BrideCardEvents).map(
          (eventType) => `${Exchanges.WEBHOOK}.bridgecard.${eventType}`,
        ),
        ...Object.values(MidenWebhookEvent).map(
          (eventType) => `${Exchanges.WEBHOOK}.miden.${eventType}`,
        ),
        ...Object.values(WemaWebhookEvent).map(
          (eventType) => `${Exchanges.WEBHOOK}.wema.${eventType}`,
        ),
      ],
    }),
    ConfigurationsModule,
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
    LogsModule,
    BullModule.forRoot({
      connection: {
        username: config.redis.username,
        password: config.redis.password,
        host: config.redis.host,
        port: config.redis.port,
      },
    }),
    AdminModule,
    NotificationModule,
    WemaCardModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
