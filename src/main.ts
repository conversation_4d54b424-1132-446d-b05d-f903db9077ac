import config from './config';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import {
  CustomValidationPipe,
  ExceptionsFilter,
  ResponseInterceptor,
} from '@crednet/utils';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const baseUrl = config.baseUrl;
  app.use(bodyParser.json({ limit: '10mb' }));
  app.setGlobalPrefix('api', { exclude: ['docs', 'health'] });
  app.useGlobalFilters(new ExceptionsFilter());
  app.useGlobalPipes(new CustomValidationPipe());
  app.useGlobalInterceptors(new ResponseInterceptor());

  if (process.env.ENV != 'production') {
    const swaggerConfig = new DocumentBuilder()
      .setTitle('Credpal')
      .setDescription('Credpal Cards Service')
      .addServer(baseUrl)
      .setVersion('v1')
      .addBearerAuth(
        {
          description: `Please enter token in following format: Bearer <JWT>`,
          name: 'Authorization',
          bearerFormat: 'Bearer',
          scheme: 'Bearer',
          type: 'http',
          in: 'Header',
        },
        'JWT',
      )
      .addServer(config.baseUrl)
      .addServer(`http://localhost:${config.port}`)
      .build();
    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup('docs', app, document);
  }
  app.enableCors({
    origin: [baseUrl, 'http://localhost:3000'],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    preflightContinue: false,
    credentials: true,
  });
  await app.listen(config.port);
}
bootstrap().then(() => {
  console.log('Service started successfully', config.port);
});
