import { Injectable } from '@nestjs/common';
import { CardService } from './card.service';
import {
  PaymentTransactionSource,
  QueryTransactionDto,
  RabbitmqService,
  ReverseTransactionInterface,
} from '@crednet/utils';
import { OnEvent } from '@nestjs/event-emitter';
import {
  BrideCardEvents,
  Events,
  Exchanges,
  PaymentEvents,
  PaymentEventTypes,
  TransactionCategory,
  TransactionStatus,
  TransactionType,
} from 'src/utils/enums';
import { TransactionRepository } from './repositories/transaction.repository';
import { CardRepository } from './repositories/card.repository';
import { UserDataRepository } from './repositories/user.repository';
import { UserData } from './entities/user.entity';

@Injectable()
export class CardEvents {
  constructor(
    private readonly cardService: CardService,
    private readonly rabbitmqService: RabbitmqService,
    private readonly transactionRepository: TransactionRepository,
    private readonly cardRepository: CardRepository,
    private readonly userRepository: UserDataRepository,
  ) {
    // setTimeout(() => {
    //   this.retryCreation({ id: '3ee3bdc9-93b8-40c9-9a0a-a5c7a35b1beb' });
    // }, 5000);
  }

  // @OnEvent(BrideCardEvents.CREATION_SUCCESS)
  // async cardCreationSuccess(data: any) {
  //   console.log('Card creation success', data);

  //   const card = await this.cardRepository.findOne({
  //     where: {
  //       cardId: data.card_id,
  //     },
  //     relations: {
  //       user: true,
  //     },
  //   });

  //   if (!card) {
  //     const user = await this.userRepository.findOne({
  //       where: {
  //         cardHolderId: data.cardholder_id,
  //       },
  //     });

  //     const pendingTxn = await this.transactionRepository.findOne({
  //       where: {
  //         userId: user.id,
  //         // status: TransactionStatus.PENDING,
  //         category: TransactionCategory.CREATE_CARD,
  //         currency: data.currency,
  //       },
  //       relations: {
  //         user: true,
  //       },
  //       order: {
  //         createdAt: -1,
  //       },
  //     });

  //     // create new card
  //     const cardDetails = await this.cardService.getCardDetailsFromBridge(
  //       data.card_id,
  //       user.userId + user.cardHolderId,
  //       user.userId,
  //     );

  //     const card = await this.cardRepository.save({
  //       userId: user.id,
  //       balance: pendingTxn.amount + '',
  //       currentLimit: '1000000',
  //       billingAddress: cardDetails.billing_address,
  //       cardId: data.card_id,
  //       cardNumber: cardDetails.card_number,
  //       cardBrand: cardDetails.brand,
  //       cardType: cardDetails.card_type,
  //       cardCurrency: cardDetails.card_currency,
  //       lastFourDigits: cardDetails.last_4,
  //       cvv: cardDetails.cvv,
  //       expiryMonth: cardDetails.expiry_month,
  //       expiryYear: cardDetails.expiry_year,
  //       issuingAppId: cardDetails.issuing_app_id,
  //       isActive: true, //virtual is auto true unlike physical card
  //       cardPin: pendingTxn.metadata['pin'],
  //     });

  //     await this.transactionRepository.update(
  //       {
  //         id: pendingTxn.id,
  //       },
  //       {
  //         status: TransactionStatus.SUCCESS,
  //         cardId: card.id,
  //       },
  //     );

  //     await this.cardService.updateCardBalance(card?.cardId);

      // this.rabbitmqService.send(
      //   Exchanges.PAYMENT_EXCHANGE,

      //   {
      //     key: PaymentRequestEventTypes.PAYMENT_REQUEST_FINALIZE,
      //     data: {
      //       reference: pendingTxn.id,
      //       responseRoutingKey: PaymentEventTypes.PAYMENT_RESPONSE_CREATE,
      //       user: user.userId,
      //       amount: +pendingTxn.amount,
      //       method: pendingTxn.metadata.method,
      //       description: 'Card Creation',
      //       category: 'virtual-card',
      //       returning_route_key: PaymentEventTypes.PAYMENT_RESPONSE_CREATE,
      //       meta: {
      //         reference: pendingTxn.id,
      //         meta: pendingTxn.metadata.meta,
      //         transactionStatus: TransactionStatus.CONFIRMED,
      //       },
      //     },
      //   },
      // );
    // }
    // return;
  // }

  // @OnEvent(BrideCardEvents.CREATION_FAILED)
  // async cardCreationFailed(data: any) {
  //   console.log('Card creation failed', data);
  //   const user = await this.userRepository.findOne({
  //     where: {
  //       cardHolderId: data.cardholder_id,
  //     },
  //   });
  //   const pendingTxn = await this.transactionRepository.findOne({
  //     where: {
  //       userId: user.id,
  //       // status: TransactionStatus.PENDING,
  //       category: TransactionCategory.CREATE_CARD,
  //       currency: data.currency,
  //     },
  //     relations: {
  //       user: true,
  //     },
  //     order: {
  //       createdAt: -1,
  //     },
  //   });
  // }

  // @OnEvent(BrideCardEvents.DEBIT_SUCCESS)
  // async debitSuccess(data: any) {
  //   console.log('debitSuccess', data);
  //   this.handleCharge(BrideCardEvents.DEBIT_SUCCESS, data);
  // }

  // @OnEvent(BrideCardEvents.DEBIT_FAILED)
  // async debitFailed(data: any) {
  //   console.log('debitFailed', data);
  //   this.handleCharge(BrideCardEvents.DEBIT_FAILED, data);
  // }

  // @OnEvent(BrideCardEvents.NAIRA_DEBIT_FAILED)
  // async ngnDebitFailed(data: any) {
  //   console.log('ngnDebitFailed', data);
  //   this.handleCharge(BrideCardEvents.NAIRA_DEBIT_FAILED, data);
  // }

  // @OnEvent(BrideCardEvents.NAIRA_DEBIT_SUCCESS)
  // async ngnDebitSuccess(data: any) {
  //   console.log('ngnDebitSuccess', data);
  //   this.handleCharge(BrideCardEvents.NAIRA_DEBIT_SUCCESS, data);
  // }

  // private async handleCharge(event: string, data: any) {
  //   console.log(data)
  //   const card = await this.cardRepository.findOne({
  //     where: {
  //       cardId: data.card_id,
  //     },
  //   });

  //   await this.transactionRepository.save({
  //     userId: card.userId,
  //     amount: +data.amount / 100,
  //     currency: data.currency,
  //     type: TransactionType.CHARGE,
  //     category: TransactionCategory.CHARGE,
  //     status:
  //       event == BrideCardEvents.DEBIT_SUCCESS ||
  //       event == BrideCardEvents.NAIRA_DEBIT_SUCCESS
  //         ? TransactionStatus.SUCCESS
  //         : TransactionStatus.FAILED,
  //     paymentStatus:
  //       event == BrideCardEvents.DEBIT_SUCCESS ||
  //       event == BrideCardEvents.NAIRA_DEBIT_SUCCESS
  //         ? TransactionStatus.SUCCESS
  //         : TransactionStatus.FAILED,
  //     cardId: card.id,
  //     narration: data.description,
  //   });

  //   await this.cardService.updateCardBalance(card?.cardId, );
  // }

  // @OnEvent(BrideCardEvents.CREDIT_SUCCESS)
  // async creditSuccess(data: any) {
  //   console.log('creditSuccess', data);
  //   this.handleTransaction(BrideCardEvents.CREDIT_SUCCESS, data);
  // }

  // @OnEvent(BrideCardEvents.CREDIT_FAILED)
  // async creditFailed(data: any) {
  //   console.log('ngnDebitSuccess', data);
  //   this.handleTransaction(BrideCardEvents.CREDIT_FAILED, data);
  // }

  // @OnEvent(BrideCardEvents.NAIRA_CREDIT_SUCCESS)
  // async nairaCreditSuccess(data: any) {
  //   console.log('nairaCreditSuccess', data);
  //   this.handleTransaction(BrideCardEvents.NAIRA_CREDIT_SUCCESS, data);
  // }

  // @OnEvent(BrideCardEvents.NAIRA_CREDIT_FAILED)
  // async nairaCreditFailed(data: any) {
  //   console.log('NAIRA_CREDIT_FAILED', data);
  //   this.handleTransaction(BrideCardEvents.NAIRA_CREDIT_FAILED, data);
  // }

  // @OnEvent(BrideCardEvents.UNLOAD_SUCCESS)
  // async unloadSuccess(data: any) {
  //   console.log('UNLOAD_SUCCESS', data);
  //   this.handleTransaction(BrideCardEvents.UNLOAD_SUCCESS, data);
  // }

  // @OnEvent(BrideCardEvents.UNLOAD_FAILED)
  // async unloadFailed(data: any) {
  //   console.log('UNLOAD_FAILED', data);
  //   this.handleTransaction(BrideCardEvents.UNLOAD_FAILED, data);
  // }

  // @OnEvent(BrideCardEvents.NAIRA_UNLOAD_SUCCESS)
  // async ngnUnloadSuccess(data: any) {
  //   console.log('NAIRA_UNLOAD_SUCCESS', data);
  //   this.handleTransaction(BrideCardEvents.NAIRA_UNLOAD_SUCCESS, data);
  // }

  // @OnEvent(BrideCardEvents.NAIRA_UNLOAD_FAILED)
  // async ngnUnloadFailed(data: any) {
  //   console.log('NAIRA_UNLOAD_FAILED', data);
  //   this.handleTransaction(BrideCardEvents.NAIRA_UNLOAD_FAILED, data);
  // }

  // private async handleTransaction(event: string, data: any) {
  //   console.log('handleTransaction  fund', data);
  //   const txn = await this.transactionRepository.findOne({
  //     where: {
  //       id: data.transaction_reference,
  //     },
  //     relations: {
  //       card: true,
  //       user: true,
  //     },
  //   });

  //   switch (event) {
  //     case BrideCardEvents.NAIRA_CREDIT_SUCCESS:
  //     case BrideCardEvents.CREDIT_SUCCESS:
  //       await this.cardService.topupSuccess(txn);

  //       break;
  //     case BrideCardEvents.NAIRA_CREDIT_FAILED:
  //     case BrideCardEvents.CREDIT_FAILED:
  //       await this.cardService.topupFailed(txn);

  //       break;
  //     case BrideCardEvents.NAIRA_UNLOAD_SUCCESS:
  //     case BrideCardEvents.UNLOAD_SUCCESS:
  //       await this.cardService.unloadSuccess(txn);

  //       break;
  //     case BrideCardEvents.NAIRA_UNLOAD_FAILED:
  //     case BrideCardEvents.UNLOAD_FAILED:
  //       await this.cardService.unloadFailed(txn);

  //       break;

  //     default:
  //       break;
  //   }
  //   await this.cardService.updateCardBalance(txn?.card?.cardId);
  // }

  @OnEvent(Events.RETRY_WITHDRAWAL)
  async retryWithdrawal(payload: { reference: string }) {
    console.log('RETRY_WITHDRAWAL', payload);
    const transaction = await this.transactionRepository.findOneBy({
      reference: payload.reference,
    });

    this.cardService.unloadSuccess(transaction);
  }

  @OnEvent(Events.SYNC_CARDHOLDER_CARDS)
  async syncAllCardholderCards(payload: { userId: string }) {
    console.log('SYNC_CARDHOLDER_CARDS', payload);
    this.cardService.syncAllCardholderCards(payload.userId);
  }

  @OnEvent(Events.UPDATE_BALANCE)
  async updateCardBalance(payload: { cardId: string; user: UserData }) {
    console.log('UPDATE_BALANCE', payload.cardId);
    this.cardService.updateCardBalance(payload.cardId);
  }

  @OnEvent(Events.VALIDATE_FUNDING_TRANSACTION)
  async validateFundingTransaction(payload: { id: string }) {
    console.log('VALIDATE_FUNDING_TRANSACTION', payload);
    const transaction = await this.transactionRepository.findOne({
      where: {
        id: payload.id,
        // status: TransactionStatus.PENDING,
        category: TransactionCategory.FUND_CARD,
      },
      relations: {
        user: true,
        card: true,
      },
    });

    this.cardService.validateFundingTransaction(transaction);
  }

  @OnEvent(Events.VALIDATE_WITHDRAWAL_TRANSACTION)
  async validateWithdrawalTransaction(payload: { id: string }) {
    console.log('VALIDATE_WITHDRAWAL_TRANSACTION', payload);
    const transaction = await this.transactionRepository.findOne({
      where: {
        id: payload.id,
        // status: TransactionStatus.PENDING,
        category: TransactionCategory.WITHDRAW_CARD,
      },
      relations: {
        user: true,
        card: true,
      },
    });

    this.cardService.validateWithdrawalTransaction(transaction);
  }

  @OnEvent(Events.RETRY_CREATION)
  async retryCreation(payload: { id: string }) {
    console.log('RETRY_CREATION', payload);
    const transaction = await this.transactionRepository.findOne({
      where: {
        id: payload.id,
        // status: TransactionStatus.PENDING,
        category: TransactionCategory.CREATE_CARD,
      },
      relations: {
        user: true,
        card: true,
      },
    });

    // await Promise.allSettled([
    //   this.cardService.createCard(
    //     transaction.reference,
    //     TransactionStatus.SUCCESS,
    //   ),
      this.cardService.verifyTransaction(transaction.reference);
    // ]);
  }

  @OnEvent(Events.VALIDATE_PENDING_TRANSACTION)
  async checkPendingPayment(payload: { id: string }) {
    const txn = await this.transactionRepository.findOne({
      where: {
        id: payload.id,
      },
      relations: {
        user: true,
        card: true,
      },
    });
    try {
      this.rabbitmqService.send(Exchanges.PAYMENT, {
        key: PaymentEvents.QUERY_TRANSACTION,
        data: {
          returningRoutingKey: PaymentEvents.CARD_PAYMENT_STATUS,
          reference: txn.reference,
        } as QueryTransactionDto,
      });

      return true;
    } catch (e) {
      console.log(e);
      console.log(e.status);
    }
  }
}
