// import { Injectable, OnModuleInit } from '@nestjs/common';
// import { CardService } from './card.service';
// import { Exchanges, PaymentEventTypes } from 'src/utils/enums';
// import { RabbitmqService } from '@crednet/utils';

// @Injectable()
// export class CardConsumer implements OnModuleInit {
//   constructor(
//     private readonly cardService: CardService,
//     private readonly rabbitmqService: RabbitmqService,
//   ) {}

//   onModuleInit() {
//     for (const eventType of Object.values(PaymentEventTypes)) {
//       this.rabbitmqService.subscribe(
//         `${Exchanges.PAYMENT}.${eventType}`,
//         async ({ data: payload, message, ack }) => {
//           console.log({ message, payload });
//           try {
//             const { status, meta } = payload;
//             if (meta) {
//               await this.callFn(eventType, meta, status, ack);
//             } else {
//               console.log('Meta not found');
//               return;
//             }
//           } catch (e) {
//             console.log(e);
//             //force the error to be thrown so that rabbitm<PERSON> can nack it in the rabbitmq service
//             throw e;
//           }
//         },
//       );
//     }
//   }

//   async callFn(event: string, meta: any, status: string, ack) {
//     try {
//       console.log({ event });
//       switch (event) {
//         case PaymentEventTypes.PAYMENT_REQUEST_CREATE:
//           await this.cardService.createCard(meta, status);
//           break;
//         case PaymentEventTypes.PAYMENT_REQUEST_FUND:
//           await this.cardService.fundCard(meta.referenceId, status);
//           break;
//         case PaymentEventTypes.PAYMENT_REQUEST_WITHDRAW:
//           await this.cardService.withdrawStatus(meta, status);
//           break;
//           case PaymentEventTypes.PAYMENT_RESPONSE_STATUS:
//             await this.cardService.updateTransactionBasedOnStatus(meta, status);
//             break;
//         case PaymentEventTypes.PAYMENT_RESPONSE_CREATE:
//         case PaymentEventTypes.PAYMENT_RESPONSE_FUND:
//           await this.cardService.updateTransaction(
//             meta.referenceId,
//             meta.transactionStatus,
//           );
//           break;

//         default:
//           break;
//       }
//       ack();
//     } catch (e) {
//       console.log(e);
//     }
//   }
// }
