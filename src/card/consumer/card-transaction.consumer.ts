import { Injectable, OnModuleInit } from '@nestjs/common';

import {
  PaymentTransaction,
  PaymentTransactionStatus,
  PaymentTransactionWalletType,
  QueryTransactionResponseDto,
  RabbitmqService,
} from '@crednet/utils';
import {
  Exchanges,
  PaymentEvents,
  TransactionStatus,
} from 'src/utils/enums';
import { CardService } from '../card.service';
import { TransactionRepository } from '../repositories/transaction.repository';
import { Transaction } from '../entities/transaction.entity';

@Injectable()
export class CardTransactionConsumer implements OnModuleInit {
  constructor(
    private readonly rmqService: RabbitmqService,
    private readonly cardService: CardService,
    private readonly transactionRepository: TransactionRepository,
  ) {}

  onModuleInit() {
    
    this.rmqService.subscribe(
      `${Exchanges.PAYMENT}.${PaymentEvents.CARD_PAYMENT_STATUS}`,
      async ({ data, ack, reject }) => {
        console.log(data, data?.status, 'status');
        try {
          if (data?.status) {
            await this.handlePaymentStatus(data, ack, reject);
          } else {
            console.log('Meta not found');
            ack();
            return;
          }
        } catch (e) {
          console.log(e);
          //force the error to be thrown so that rabbitmq can nack it in the rabbitmq service
          throw e;
        } finally {
          ack();
        }
      },
    );
  }

  async handlePaymentStatus(
    payload: QueryTransactionResponseDto,
    ack: Function,
    reject: Function,
  ) {
    try {
      const { reference, status, transaction } = payload;
      let data = await this.transactionRepository.findOne({
        where: { reference },
      });

      if (!data) {
        data = await this.transactionRepository.findOne({
          where: { id: reference },
        });

        if (!data) {
          return;
        }
      }

      if (
        (data?.paymentStatus != TransactionStatus.SUCCESS &&
          data?.paymentStatus != TransactionStatus.FAILED) ||
        (status == PaymentTransactionStatus.REVERSED && !data?.isRefunded)
      ) {
        switch (status) {
          case PaymentTransactionStatus.SUCCESSFUL:
            await this.handleSuccess(data, transaction);
            break;

          case PaymentTransactionStatus.FAILED:
            await this.handleFailed(data, transaction, payload.error);
            break;

          case PaymentTransactionStatus.REVERSED:
            await this.handleRefunded(data);
            break;

          case PaymentTransactionStatus.NOT_FOUND:
            if (
              data.paymentStatus == TransactionStatus.PENDING ||
              data.paymentStatus == TransactionStatus.PROCESSING
            ) {
              await this.handleNotFound(data, transaction);
            }
            break;

          default:
            break;
        }
      }
      // ack();
    } catch (e) {
      console.log(e);
      if (String(e?.message).includes('already exist')) {
        // ack();
        return;
      }

      // reject();
    }
  }

  private async handleSuccess(
    data: Transaction,
    transaction: PaymentTransaction,
  ) {
    const update = { paymentStatus: TransactionStatus.SUCCESS };
    if (transaction.walletType) {
      update['walletType'] = transaction.walletType;
    }
    await this.transactionRepository.update({ id: data.id }, update);

    await this.cardService.finalizePayment(data, transaction);
  }

  private async handleFailed(
    data: Transaction,
    transaction: PaymentTransaction,
    error: object,
  ) {
    const update = {
      paymentStatus: TransactionStatus.FAILED,
      status: TransactionStatus.FAILED,
      errors: error,
    };
    if (data.walletType) {
      update['walletType'] = transaction.walletType;
    }
    await this.transactionRepository.update({ id: data.id }, update);

    // const template =
    //   data.category == TransactionCategory.FUND_CARD
    //     ? NotificationTemplates.CARD_FUNDING_FAILED
    //     : NotificationTemplates.CARD_WITHDRAWAL_FAILED;

    // await this.rmqService.send(Exchanges.NOTIFICATION, {
    //   key: Events.CARDS_NOTIFICATION,
    //   data: {
    //     template,
    //     userId: data.user?.userId,
    //     parameter: {
    //       amount: data.amount,
    //       reference: data.reference,
    //       wallet: data.walletType,
    //       serviceType: data.category,
    //     },
    //   } as SendNotificationPayload,
    // });
  }

  private async handleRefunded(data: Transaction) {
    const update = { isRefunded: true };
    if (
      data.status == TransactionStatus.PENDING ||
      data.status == TransactionStatus.PROCESSING
    ) {
      update['status'] = TransactionStatus.FAILED;
    }

    if (
      data.paymentStatus == TransactionStatus.PROCESSING ||
      data.paymentStatus == TransactionStatus.PENDING
    ) {
      update['paymentStatus'] = TransactionStatus.FAILED;
    }
    await this.transactionRepository.update({ id: data.id }, update);
  }

  private async handleNotFound(
    data: Transaction,
    transaction: PaymentTransaction,
  ) {
    const paymentWalletResponse = { ...data.metadata?.paymentWalletResponse };

    if (!paymentWalletResponse[transaction.walletType]) {
      paymentWalletResponse[transaction.walletType] = transaction.status;
    }
    const metadata = { ...data.metadata, paymentWalletResponse };

    await this.transactionRepository.update({ id: data.id }, { metadata });

    if (
      paymentWalletResponse[PaymentTransactionWalletType.CREDPAL_CREDIT] ==
        PaymentTransactionStatus.NOT_FOUND &&
      paymentWalletResponse[PaymentTransactionWalletType.CREDPAL_CASH] ==
        PaymentTransactionStatus.NOT_FOUND
    ) {
      const billAge = Date.now() - data.createdAt.getTime();
      const tenMinutesInMs = 10 * 60 * 1000; // 10 minutes in milliseconds

      if (billAge >= tenMinutesInMs) {
        await this.transactionRepository.update(
          { id: data.id },
          {
            metadata,
            status: TransactionStatus.ABANDONED,
            paymentStatus: TransactionStatus.ABANDONED,
          },
        );
      }
    }
  }
}
