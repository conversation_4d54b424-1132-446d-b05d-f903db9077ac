import { Module } from '@nestjs/common';
import { CardService } from './card.service';
import { CardController } from './card.controller';
import { CardRepository } from './repositories/card.repository';
import { UserDataRepository } from './repositories/user.repository';
import { TransactionRepository } from './repositories/transaction.repository';
// import { CardConsumer } from './card.consumer';
import { CardProducer } from './card.producer';
import { ConfigurationsModule } from 'src/configurations/configurations.module';
import { LogRepository } from './repositories/log.repository';
import { BridgecardModule } from '@app/bridgecard';
import { CardEvents } from './card.events';
import { ConfirmTransactionProcessor } from './processors/confirm-transaction.processor';
import { BullModule } from '@nestjs/bullmq';
import { LocalQueues } from 'src/utils/enums';
import { MidenModule } from '@app/miden';
import { VerificationModule } from '@app/verification';
import { MidenWebhookConsumer } from './miden-webhook.consumer';
import { PaymentCacheModule } from '@crednet/utils';
import { CardTransactionConsumer } from './consumer/card-transaction.consumer';
import { GeolocationModule } from '@app/geolocation';
import { MigrationService } from './migrate';
import { NotificationModule } from 'src/notification/notification.module';

@Module({
  imports: [
    BridgecardModule,
    ConfigurationsModule,
    MidenModule,
    VerificationModule,
    PaymentCacheModule,
    GeolocationModule,
    NotificationModule,
    BullModule.registerQueue({
      name: LocalQueues.TRANSACTION,
    }),
  ],
  providers: [
    CardService,
    CardRepository,
    UserDataRepository,
    TransactionRepository,
    LogRepository,
    // CardConsumer,
    CardProducer,
    ConfirmTransactionProcessor,
    CardEvents,
    MidenWebhookConsumer,
    CardTransactionConsumer,
    MigrationService
  ],
  controllers: [CardController],
  exports: [CardService],
})
export class CardModule {}
