import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { Transaction } from './transaction.entity';
import { UserData } from './user.entity';
import { Currency } from '@crednet/utils';
import { CardProvider } from '../dto/card-provider';

@Entity({
  name: 'cards',
})
export class Card extends BaseEntity {
  @Column()
  @Index('userId')
  userId: string;


  @Column()
  @Index('cardId')
  cardId: string;

  @Column()
  @Index()
  cardType: string;

  @Column({ nullable: true })
  issuingAppId: string;

  @Column({ nullable: true })
  cardNumber: string;

  @Column({ nullable: true })
  cardBrand: string;

  @Column({ nullable: true })
  cardPin: string;

  @Column({
    nullable: true,
    type: 'enum',
    enum: Currency,
    default: Currency.USD,
  })
  cardCurrency: string;

  @Column({ nullable: true })
  lastFourDigits: string;

  @Column({ nullable: true })
  firstSixDigits: string;

  @Column({ nullable: true })
  fingerprint: string;

  @Column({ nullable: true })
  cvv: string;

  @Column({ nullable: true })
  expiryMonth: string;

  @Column({ nullable: true })
  expiryYear: string;

  @Column({ nullable: true, default: '0' })
  balance: string;

  @Column({ nullable: true, default: '5000' })
  currentLimit: string;

  @Column({ nullable: true, type: 'json' })
  billingAddress: any;

  @Column({ type: Boolean })
  isActive: boolean;

  @Column({ type: Boolean, default: false })
  isDeleted: boolean;


  @Column({ nullable: true,})
  terminationReason: string;

  @OneToMany(() => Transaction, (transaction) => transaction.card)
  transactions: Transaction[];

  @ManyToOne(() => UserData, (user) => user.cards)
  @JoinColumn({ name: 'userId', referencedColumnName: 'id' })
  user: UserData;

  @Column({
    nullable: false,
    type: 'enum',
    enum: CardProvider,
    default: CardProvider.BRIDGECARD,
  })
  @Index('provider')
  provider: CardProvider;

  @Column({ nullable: true })
  cardTheme: string;

  @Column({ nullable: true })
  cardLabel: string;
}
