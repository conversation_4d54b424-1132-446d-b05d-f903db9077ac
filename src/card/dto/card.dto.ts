import { Currency } from '@crednet/utils';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsDefined,
  IsEnum,
  IsNotEmpty,
  IsNotEmptyObject,
  IsOptional,
  IsString,
  Length,
} from 'class-validator';
import { CardType, IdType } from 'src/utils/enums';

export class Address {
  @ApiProperty()
  @IsString()
  address: string;
  @ApiProperty()
  @IsString()
  city: string;
  @ApiProperty()
  @IsString()
  state: string;
  @ApiProperty()
  @IsString()
  country: string;
  @ApiProperty()
  @IsString()
  postalCode: string;
  @ApiProperty()
  @IsString()
  houseNo: string;
}

export class Identity {
  @ApiProperty()
  @IsString()
  @IsEnum(IdType)
  idType: IdType;
  @ApiProperty()
  @IsOptional()
  idNumber?: string;
  @ApiProperty()
  @IsOptional()
  idImage: string;
  @ApiProperty()
  @IsString()
  @Length(11, 11)
  bvn: string;
}

export class PinDto {
  @ApiProperty()
  @IsString()
  pin: string;
}

export class CreateCardDto {
  @ApiProperty()
  @IsDefined()
  meta: any;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  method: string;

  // @ApiProperty()
  // @IsDefined()
  // @IsNotEmpty()
  // @IsEnum(CardType)
  // cardType: string;

  // @ApiProperty()
  // @IsDefined()
  // @IsNotEmpty()
  // cardBrand: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardLimit: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  referenceId: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  pin: string;
}

export class RegisterUser {
  @ApiProperty()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmptyObject()
  address: Address;

  @ApiProperty()
  @IsDefined()
  @IsNotEmptyObject()
  identity: Identity;
}

export class ActivatePhysicalCardDto extends PinDto {
  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardType: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardBrand: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardCurrency: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardTokenNumber: string;
}

export class InitiateFundCardDto extends PinDto {
  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardId: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  method: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  amount: string;

  @ApiProperty({ enum: Currency })
  @IsOptional()
  @IsEnum(Currency)
  currency: Currency;

  @ApiProperty()
  @IsDefined()
  meta: any;
}

export enum CardTheme {
  classic = 'classic',
  blue = 'blue',
  black = 'black',
  orange = 'orange',
  lemon = 'lemon',
}
export class InitiateCreateCardDto extends PinDto {
  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  amount: number;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  method: string;

  @ApiProperty({ enum: Currency })
  @IsDefined()
  @IsNotEmpty()
  @IsEnum(Currency)
  currency: Currency;

  // @ApiProperty()
  // @IsDefined()
  // meta: any;

  @ApiProperty()
  @IsOptional()
  @IsEnum(CardType)
  cardTheme: CardTheme;

  @ApiProperty()
  @IsOptional()
  cardLabel: string;

  // @ApiProperty()
  // @IsDefined()
  // @IsNotEmpty()
  // cardBrand: string;

  // @ApiProperty()
  // @IsDefined()
  // @IsNotEmpty()
  // cardLimit: string;

  // @ApiProperty()
  // @IsDefined()
  // @IsNotEmpty()
  // pin: string;
}

export class InitateFundCardDto {
  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  method: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardId: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  amount: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  currency: string;
}

export class FundCardDto {
  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  method: string;

  @ApiProperty()
  @IsDefined()
  meta: any;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  referenceId: string;
}

export class UnloadCardDto extends PinDto {
  @ApiProperty()
  @IsOptional()
  meta: any;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardId: string;

  @ApiProperty()
  @IsDefined()
  // @IsNotEmpty()
  amount: number;
}

export class GetCardTransactionsDto {
  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardId: string;

  @ApiProperty()
  @IsOptional()
  page: number;
}

export class GetCardOTP {
  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardId: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  amount: number;
}

export class GetCardTransactionByIdDto {
  @ApiProperty()
  @IsDefined()
  @IsOptional()
  cardId: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  transactionId: string;
}

export class UpdateTransactionByIdDto {
  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  referenceId: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  status: string;
}

export class FreezeCardDto {
  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardId: string;
}

export class UnfreezeCardDto {
  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardId: string;
}

export class DeleteCardDto {
  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardId: string;
}

export class UpdateCardPinDto {
  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  cardId: string;

  @ApiProperty()
  @IsDefined()
  @IsNotEmpty()
  newPin: string;
}
