import {
  BadRequestException,
  Injectable,
  UnprocessableEntityException,
} from '@nestjs/common';

import {
  ActivatePhysicalCardDto,
  DeleteCardDto,
  FreezeCardDto,
  GetCardTransactionByIdDto,
  GetCardTransactionsDto,
  InitiateCreateCardDto,
  InitiateFundCardDto,
  RegisterUser,
  UnfreezeCardDto,
  UnloadCardDto,
  UpdateCardPinDto,
  UpdateTransactionByIdDto,
} from './dto/card.dto';
import { CardRepository } from './repositories/card.repository';
import { UserDataRepository } from './repositories/user.repository';
import { TransactionRepository } from './repositories/transaction.repository';
import {
  CardType,
  ConfigurationKeys,
  ExchangeCategory,
  Exchanges,
  LocalQueues,
  MidenCardTransactionStatus,
  MidenWebhookEvent,
  PaymentEvents,
  PaymentTypes,
  TransactionCategory,
  TransactionStatus,
  TransactionType,
} from 'src/utils/enums';
import { IsNull, Like, MoreThan } from 'typeorm';
import { AuthData } from '@crednet/authmanager';
import { Transaction } from './entities/transaction.entity';
import { UserData } from './entities/user.entity';
import { ConfigurationsService } from 'src/configurations/configurations.service';
import { BridgecardService } from '@app/bridgecard';
import { EncryptionService } from '../../libs/bridgecard/src/encryption.service';
import { checkPnd } from 'src/utils/utils';
import {
  Currency,
  PaymentCacheService,
  PaymentTransaction,
  PaymentTransactionSource,
  PaymentTransactionType,
  RabbitmqService,
  RequestWithdrawalDto,
  ReverseTransactionInterface,
} from '@crednet/utils';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { VerificationService } from '@app/verification';
import { MidenService } from '@app/miden';
import { CardProvider } from './dto/card-provider';
import { randomUUID } from 'crypto';
import { MidenCardTransactionResponse } from '@app/miden/dto';
import { GeolocationService } from '@app/geolocation';
import { BillingAddress } from '@app/miden/billing-address';
import { NotificationService } from 'src/notification/notification.service';
import config from 'src/config';

@Injectable()
export class CardService {
  constructor(
    private readonly bridgecardService: BridgecardService,
    private readonly cardRepository: CardRepository,
    private readonly userRepository: UserDataRepository,
    private readonly transactionRepository: TransactionRepository,
    private readonly currencyConverter: ConfigurationsService,
    private readonly configurationsService: ConfigurationsService,
    private readonly encryptionService: EncryptionService,
    private readonly verificationService: VerificationService,
    @InjectQueue(LocalQueues.TRANSACTION)
    private readonly transactionQueue: Queue,
    private readonly midenService: MidenService,
    private readonly rmqService: RabbitmqService,
    private readonly paymentCacheService: PaymentCacheService,
    private readonly geolocationService: GeolocationService,
    private readonly notificationService: NotificationService,
  ) {
    // setTimeout(() => {
    //   this.updateTransactionBasedOnStatus();
    // }, 5000);
  }

  async loadMissingtransactions(page = 1) {
    const transactions = await this.midenService.cardTransactions(page);
    // console.log('data', transactions);

    for (const data of transactions.transactions) {
      console.log(
        'data',
        data?.description,
        data?.transactionStatus,
        data?.transactionAmount,
      );
      // continue;
      const txn = await this.transactionRepository.findOne({
        where: { reference: data.id.toString() },
      });

      if (txn) {
        continue;
      }

      // console.log('handleCharge', data?.data?.amount, data?.data?.orderNumber);
      const card = await this.cardRepository.findOne({
        where: {
          cardId: data.cardId,
        },
        relations: { user: true },
      });
      if (!card) {
        continue;
      }
      const status =
        data?.transactionStatus == 'Approved'
          ? TransactionStatus.SUCCESS
          : TransactionStatus.FAILED;

      console.log('save transaction', data?.transactionAmount);
      await this.transactionRepository.insert({
        amount: data?.transactionAmount ?? data?.authorizationAmount,
        fee: 0,
        user: { id: card.user?.id },
        currency: data.currencyCode == 'NGN' ? Currency.NGN : Currency.USD,
        type: TransactionType.CHARGE,
        category: TransactionCategory.CHARGE,
        provider: CardProvider.MIDEN,
        status,
        paymentStatus: status,
        card: { id: card.id },
        narration: data?.description ?? data.merchantName,
        metadata: data as any,
        reference: data?.id.toString(),
        createdAt: data?.transactionTime,
        updatedAt: data?.transactionTime,
      });

      try {
        await this.updateCardBalance(data?.cardId);
      } catch (error) {
        console.log(error);
      }
    }

    if (transactions.totalPages > page) {
      await this.loadMissingtransactions(page + 1);
    }
  }

  async isUserRegistered(userId: string) {
    console.log(userId);
    const user = await this.userRepository.findOne({
      where: { userId: userId, deletedAt: IsNull() },
      relations: { cards: true },
      order: {
        cards: {
          createdAt: 'DESC', // Order cards by createdAt field in descending order (latest first)
        },
      },
    });

    if (user?.cards) {
      for (const card of user?.cards) {
        // if (card?.id && !card?.cardNumber) {
        //   await this.syncAllCardholderCards(user.id);
        //   break;
        // }
      }
    }
    return user;
  }

  async register(authData: AuthData, dto: RegisterUser) {
    let user = await this.userRepository.findOne({
      where: { userId: authData.id + '' },
    });

    if (user) {
      return { ...user };
    }

    if (dto.address?.state.includes('State')) {
      dto.address.state = dto.address?.state.split(' ')[0];
    }

    try {
      // const verifyFace = await this.verificationService.verifyFace({
      //   bvn: dto.identity.bvn,
      //   imageUrl: dto.identity.idImage,
      // });

      // if (verifyFace.confidence < 85) {
      //   throw new BadRequestException(
      //     'Face verification failed, please try again',
      //   );
      // }

      await this.userRepository.save({
        userId: authData.id + '',
        firstName: authData.name,
        lastName: authData.last_name,
        email: authData.email,
        phoneNumber: authData.phone_no,
        address: {
          address: dto.address?.address,
          city: dto.address?.city,
          state: dto.address?.state,
          country: dto.address?.country,
          postal_code: dto.address?.postalCode,
          house_no: dto.address?.houseNo,
        },
        idImage: dto.identity.idImage,
        bvn: dto.identity.bvn,
        idNumber: dto.identity.idNumber,
        idType: dto.identity.idType,
      });

      return user;
    } catch (e) {
      console.log(e);
      throw e;
    }
  }

  async throttleCall(
    userId: string,
    currency: Currency,
    category: TransactionCategory,
    type: TransactionType,
    minute: number = 2,
  ) {
    // reject if successful creation txn was made at most 2 minutes ago
    const minutesAgo = new Date(Date.now() - minute * 60 * 1000); // Calculate the time 2 minutes ago and reduce by 1 hr because of timezone

    const pastTxn = await this.transactionRepository.findOne({
      where: {
        user: { id: userId },
        currency: currency,
        // status: TransactionStatus.SUCCESS,
        type,
        category,
        createdAt: MoreThan(minutesAgo),
      },
      order: {
        createdAt: 'DESC',
      },
    });

    if (pastTxn) {
      throw new BadRequestException(
        `${category.split('_').join(' ')} transaction recently called, please wait ${minute} minutes and try again`,
      );
    }
  }

  async getCreationAmountAndFee(currency: Currency, amount: number) {
    const fee = await this.configurationsService.getConfigurationValue(
      currency == 'USD'
        ? ConfigurationKeys.USD_CARD_CREATION_FEE
        : ConfigurationKeys.NGN_CARD_CREATION_FEE,
      currency == 'USD' ? 3 : 1000,
    );
    let amt = amount + parseFloat(fee);
    let exchangeRate = 0;

    if (amount == 0 && currency == Currency.USD) {
      throw new BadRequestException('The minimum balance for a USD card is $1');
    }

    if (currency == 'USD') {
      try {
        exchangeRate = await this.currencyConverter.convertCurrencyUsd(
          1,
          ExchangeCategory.DEPOSIT,
        );
        amt = exchangeRate * Number(amt);
      } catch (e) {
        console.log(e);
        throw e;
      }
    }

    return { amount: amt, fee, exchangeRate };
  }

  async initiateCreate(authData: AuthData, dto: InitiateCreateCardDto) {
    checkPnd(authData);

    try {
      const user = await this.userRepository.findOne({
        where: { userId: authData.id + '' },
      });

      if (!user) {
        throw new BadRequestException('User Not found');
      }

      const card = await this.cardRepository.findOne({
        where: {
          userId: authData.id + '',
          cardCurrency: dto.currency,
          provider: CardProvider.MIDEN,
        },
      });

      if (card) {
        throw new BadRequestException(
          'You cannot have more than one card per currency',
        );
      }

      await this.throttleCall(
        user.id,
        dto.currency,
        TransactionCategory.CREATE_CARD,
        TransactionType.CREDIT,
      );

      /// reject new creation if a transaction for creation is already in progress
      // TODO:: uncomment this when the payment request is implemented
      const pending = await this.transactionRepository.findOne({
        where: {
          userId: user.id,
          status: TransactionStatus.PENDING,
          category: TransactionCategory.CREATE_CARD,
          currency: dto.currency,
        },
      });

      if (pending) {
        this.createCard(pending.reference, TransactionStatus.SUCCESS);
        throw new BadRequestException(
          'A card creation is already in progress, please wait for it to complete',
        );
      }

      // const createdCards = await this.getAllCardholderCards(user.userId);
      // const createdCard = createdCards.cards.find(
      //   (card) => card.card_currency == dto.currency && card.is_deleted,
      // );

      //   if (createdCard) {
      //     const cardId = createdCard.card_id;
      //     const cardDetails = await this.getCardDetailsFromBridge(
      //       cardId,
      //       user.userId + user.cardHolderId,
      //       user.id,
      //     );
      //     await this.cardRepository.save({
      //       userId: user.id,
      //       balance: +cardDetails.balance / 100 + '',
      //       currentLimit: '10000',
      //       billingAddress: cardDetails.billing_address,
      //       cardId: cardDetails.card_id,
      //       cardNumber: cardDetails.card_number,
      //       cardBrand: cardDetails.brand,
      //       cardType: cardDetails.card_type,
      //       cardCurrency: cardDetails.card_currency,
      //       lastFourDigits: cardDetails.last_4,
      //       cvv: cardDetails.cvv,
      //       expiryMonth: cardDetails.expiry_month,
      //       expiryYear: cardDetails.expiry_year,
      //       issuingAppId: cardDetails.issuing_app_id,
      //       isActive: true,
      //       cardPin: this.encryptionService.encryptDeep(
      //         user.userId + user.cardHolderId,
      //         pin,
      //       ),
      //     });

      //     await this.transactionRepository.update(
      //       { id: pending.id },
      //       {
      //         status: TransactionStatus.SUCCESS,
      //         paymentStatus: TransactionStatus.FAILED,
      //       },
      //     );
      //     throw new BadRequestException('Card already created');
      //   }
      //   throw new BadRequestException('A card creation is already in progress');
      // }

      const { amount, fee, exchangeRate } = await this.getCreationAmountAndFee(
        dto.currency,
        dto.amount,
      );
      const reference = `card-${randomUUID()}`;

      const meta = {};
      if (dto.currency == 'USD') {
        meta['effectiveExchangeRate'] = exchangeRate;
      }

      const metadata: any = {
        user: authData.id,
        amount,
        method: dto.method,
        reference: reference,
        currency: dto.currency,
        meta,
        cardType: CardType.VIRTUAL,
        cardBrand: 'Mastercard',
        cardLimit: '10000',

        type: PaymentTypes.CREATE,
        cardTheme: dto.cardTheme,
        cardLabel: dto.cardLabel,
      };

      const txn = await this.transactionRepository.save({
        user: { id: user.id },
        amount: +dto.amount,
        fee,
        provider: CardProvider.MIDEN,
        reference,
        category: TransactionCategory.CREATE_CARD,
        currency: dto.currency,
        type: TransactionType.CREDIT,
        status: TransactionStatus.PENDING,
        paymentStatus: TransactionStatus.PENDING,
        narration: 'Card Creation',
        exchangeRate,
        metadata,
      });

      await this.paymentCacheService.savePayment({
        source: PaymentTransactionSource.CARDS_SERVICE,
        userId: user.userId,
        currency: Currency.NGN,
        reference,
        amount,
        description: txn.narration,
        returningRoutingKey: PaymentEvents.CARD_PAYMENT_STATUS,
        meta: {
          reference: txn.reference,
          amount: txn.amount,
          wallet: txn.currency,
        },
      });

      return txn;
    } catch (e) {
      console.log(e);
      throw new UnprocessableEntityException(e);
    }
  }

  async getMetaFromTransaction(transactionId: string) {
    return await this.transactionRepository.findOne({
      where: {
        id: transactionId,
      },
      relations: { card: true, user: true },
    });
  }

  async createCard(reference: string, status: string) {
    console.log(reference, status);

    if (
      status.toLowerCase() !== 'success' &&
      status.toLowerCase() !== 'pending'
    ) {
      return await this.transactionRepository.update(
        {
          reference,
        },
        {
          status: TransactionStatus.FAILED,
          paymentStatus: TransactionStatus.FAILED,
        },
      );
    }

    // console.log('create card::', reference, status);

    const txn = await this.transactionRepository.findOne({
      where: {
        reference,
        // status: TransactionStatus.PENDING,
      },
      relations: { user: true },
    });
    // console.log('create card::', reference, txn);

    if (!txn) {
      throw new BadRequestException('transaction not found');
    }

    if (txn.status != TransactionStatus.PENDING) {
      throw new BadRequestException('transaction already finalized');
    }

    const user = await this.userRepository.findOne({
      where: { id: txn.userId },
    });
    // // console.log(user, dto.pin, this.encryptionService.decryptDeep(user.userId + user.cardHolderId, txn?.metadata?.pin))
    // const encryptedPin = this.encryptionService.encryptBridgeCardPin(
    //   this.encryptionService.decryptDeep(
    //     user.userId + user.cardHolderId,
    //     txn?.metadata?.pin,
    //   ),
    // );
    if (txn.paymentStatus != TransactionStatus.SUCCESS) {
      await this.transactionRepository.update(
        {
          reference,
        },
        {
          paymentStatus: TransactionStatus.SUCCESS,
        },
      );
    }
    if (!user) {
      throw new BadRequestException('transaction not found');
    }
    const cardCurrency = txn.currency;
    // const amount = +txn.amount * 100 + ''; // as cents
    // const addressConponents = await this.geolocationService.getPostalAddress(
    //   `${user.address['address']}, ${user.address['city']}, ${user.address['state']}`,
    // );
    console.log('create card::', reference, user.address);

    try {
      const data = await this.midenService.createCard({
        lastName: user.lastName,
        firstName: user.firstName,
        phone: user.phoneNumber,

        // String(addressConponents?.address ?? user.address['address'])
        //   .length >= 50
        // ? (addressConponents?.address ?? user.address['address']).substring(
        //     0,
        //     50,
        //   )
        // : (addressConponents?.address ??
        address1: user.address['address'].substring(0, 50),
        email: user.email,
        city:
          // String(user.address['city']).length >= 20
          //   ? (addressConponents?.city ?? user.address['city'].substring(0, 20))
          //   :

          user.address['city'].replace('/', '').substring(0, 20),
        state: user.address['state'].substring(0, 20),
        //addressConponents?.postalCode ??
        zipcode: user.address['postal_code'],
        country: 'NG',
        idNumber: user.bvn,
        idType: 'BVN',
        customerBvn: user.bvn,
        initialBalance: +txn.amount,
        cardBrand: 'Mastercard',
        clientReference: txn.id,
        cardCurrency: cardCurrency,
        walletCurrency: cardCurrency,
      });

      const details = data.data;

      if (!user.midenCardHolderId) {
        await this.userRepository.update(
          { id: user.id },
          { midenCardHolderId: details.cardCustomerId },
        );
      }

      const encryptionKey = user.userId + user.id;
      const decryptedDetails = this.encryptionService.decryptMidenAES(
        details.secureCardDetails,
        config.miden.clientId,
      );
      // console.log(decryptedDetails);

      const card = await this.cardRepository.save({
        userId: user.id,
        balance: details.availableBalance + '',
        currentLimit: '5000',
        provider: CardProvider.MIDEN,
        cardId: details.cardId,
        cardNumber: this.encryptionService.encryptDeep(
          encryptionKey,
          decryptedDetails.CardNumber,
        ),
        cardBrand: 'Mastercard',
        cardType: 'Virtual',
        cardTheme: txn.metadata.cardTheme,
        cardLabel: txn.metadata.cardLabel,
        cardCurrency: cardCurrency,
        lastFourDigits: details.lastFour,
        cvv: this.encryptionService.encryptDeep(
          encryptionKey,
          decryptedDetails.SecurityCode,
        ),
        expiryMonth: this.encryptionService.encryptDeep(
          encryptionKey,
          String(decryptedDetails.Expiration.split('-')[1]),
        ),
        expiryYear: this.encryptionService.encryptDeep(
          encryptionKey,
          String(decryptedDetails.Expiration.split('-')[0]),
        ),
        issuingAppId: data.transactionWallet,
        isActive: true, //virtual is auto true unlike physical card
        billingAddress: {
          state: BillingAddress.state,
          billing_city: BillingAddress.city,
          country_code: BillingAddress.country,
          billing_country: BillingAddress.country,
          billing_address1: BillingAddress.address1,
          billing_zip_code: BillingAddress.zipcode,
        } as any,
      });
      await this.saveCardDetails(details.cardId, user, txn);
      await this.transactionRepository.update(
        {
          reference,
        },
        {
          status: TransactionStatus.SUCCESS,
          cardId: card.id,
        },
      );
      this.notificationService.cardCreation(txn, user.userId);

      // await this.syncAllCardholderCards(user.id);

      // this.cardProducer.sendMessage(
      //   PaymentRequestEventTypes.PAYMENT_REQUEST_FINALIZE,
      //   {
      //     reference: txn.id,
      //     responseRoutingKey: PaymentEventTypes.PAYMENT_RESPONSE_CREATE,
      //     user: user.userId,
      //     amount: +dto.amount,
      //     method: dto.method,
      //     description: 'Card Creation',
      //     category: 'virtual-card',
      //     returning_route_key: PaymentEventTypes.PAYMENT_RESPONSE_CREATE,
      //     meta: {
      //       reference: txn.id,
      //       meta: dto.meta,
      //       transactionStatus: TransactionStatus.CONFIRMED,
      //     },
      //   },
      // );

      this.getCardDetails(details.cardId);

      return card;
    } catch (e) {
      console.log(e);

      if (
        e['responseCode'] == '119' &&
        e['data'] &&
        e['data']['cardCustomerId']
      ) {
        const data = await this.getCardIdByCustomerId(
          e['data']['cardCustomerId'],
          reference,
          user.id,
        );

        // await this.midenService.reIssueCard({
        //   cardCustomerId: e['data']['cardCustomerId'],
        //   initialBalance: +txn.amount,
        //   cardBrand: 'Mastercard',
        //   nameOnCard: user.firstName + ' ' + user.lastName,
        //   clientReference: txn.reference,
        //   walletCurrency: txn.currency,
        // });
        if (data?.cardId) {
          const card = await this.saveCardDetails(data.cardId, user, txn);

          await this.transactionRepository.update(
            {
              reference,
            },
            {
              status: TransactionStatus.SUCCESS,
              cardId: card.id,
            },
          );
          this.notificationService.cardCreation(txn, user.userId);
          return card;
        }
      }

      throw new UnprocessableEntityException(e);
    }
  }

  async saveCardDetails(cardId: string, user: UserData, txn?: Transaction) {
    const encryptionKey = user.userId + user.id;

    const cardDetails = await this.midenService.getCardDetails(cardId);
    if (!cardDetails?.data) return;
    let card = await this.cardRepository.findOne({ where: { cardId } });
    const details = cardDetails?.data;
    const decryptedDetails = this.encryptionService.decryptMidenAES(
      details.secureCardDetails,
      config.miden.clientId,
    );

    if (card) {
      await this.cardRepository.update(
        { id: card.id },
        {
          cardNumber: this.encryptionService.encryptDeep(
            encryptionKey,
            decryptedDetails.CardNumber,
          ),
          cvv: this.encryptionService.encryptDeep(
            encryptionKey,
            decryptedDetails.SecurityCode,
          ),
          expiryMonth: this.encryptionService.encryptDeep(
            encryptionKey,
            String(decryptedDetails.Expiration.split('-')[1]),
          ),
          expiryYear: this.encryptionService.encryptDeep(
            encryptionKey,
            String(decryptedDetails.Expiration.split('-')[0]),
          ),

          billingAddress: {
            state: BillingAddress.state,
            billing_city: BillingAddress.city,
            country_code: BillingAddress.country,
            billing_country: BillingAddress.country,
            billing_address1: BillingAddress.address1,
            billing_zip_code: BillingAddress.zipcode,
          } as any,
        },
      );
    } else {
      card = await this.cardRepository.save({
        userId: user.id,
        balance: details.availableBalance + '',
        currentLimit: '5000',
        provider: CardProvider.MIDEN,
        cardId: cardId,
        cardTheme: txn?.metadata?.cardTheme,
        cardLabel: txn?.metadata?.cardLabel,
        cardBrand: 'Mastercard',
        cardType: 'Virtual',
        cardCurrency: details.currencyCode,
        firstSixDigits: details.firstSix,
        lastFourDigits: details.lastFour,
        fingerprint: details.fingerprint,
        cardNumber: this.encryptionService.encryptDeep(
          encryptionKey,
          decryptedDetails.CardNumber,
        ),
        cvv: this.encryptionService.encryptDeep(
          encryptionKey,
          decryptedDetails.SecurityCode,
        ),
        expiryMonth: this.encryptionService.encryptDeep(
          encryptionKey,
          String(decryptedDetails.Expiration.split('-')[1]),
        ),
        expiryYear: this.encryptionService.encryptDeep(
          encryptionKey,
          String(decryptedDetails.Expiration.split('-')[0]),
        ),
        issuingAppId: details.gatewayMerchantGuid,
        isActive: true, //virtual is auto true unlike physical card
        billingAddress: {
          state: BillingAddress.state,
          billing_city: BillingAddress.city,
          country_code: BillingAddress.country,
          billing_country: BillingAddress.country,
          billing_address1: BillingAddress.address1,
          billing_zip_code: BillingAddress.zipcode,
        } as any,
      });
    }

    return card;
  }

  async getCardIdByCustomerId(
    customerId: string,
    reference: string,
    userId: string,
  ): Promise<any> {
    const response = await this.midenService.getCardCustomers(customerId);
    if (
      response.isSuccessful &&
      response.customers.length &&
      response.customers[0].customerCards.length
    ) {
      console.log(response.customers[0]?.customerCards);
      const cards = response.customers[0].customerCards.filter((card) =>
        card.status.includes('Active'),
      );

      if (cards.length > 0) {
        return cards[0];
      }

      // return null;
    }

    const latestCard = await this.cardRepository.findOne({
      where: {
        userId: userId,
      },
      order: { createdAt: 'DESC' },
    });
    console.log(latestCard);

    if (latestCard) {
      const cardDetails = await this.midenService.getCardDetails(
        latestCard.cardId,
      );
      console.log(
        cardDetails,
        cardDetails?.data?.balanceAsAtTermination &&
          cardDetails.data.status?.toLowerCase().includes('deactivated'),
      );
      if (
        cardDetails?.data?.balanceAsAtTermination &&
        cardDetails.data.status?.toLowerCase().includes('deactivated')
      ) {
        // return latestCard;

        const txn = await this.transactionRepository.findOne({
          where: {
            reference,
          },
          relations: {
            user: true,
          },
        });
        const card = await this.midenService.reIssueCard({
          cardCustomerId: response.customers[0].customerId,
          initialBalance:
            parseFloat(txn.amount.toString()) +
            parseFloat(
              cardDetails?.data?.balanceAsAtTermination?.toString() ?? '0',
            ),
          cardBrand: 'Mastercard',
          nameOnCard: txn.user.firstName + ' ' + txn.user.lastName,
          clientReference: txn.reference,
          walletCurrency: txn.currency,
        });
        // this.saveCardDetails(card.data.cardId, txn.user);
        return card.data;
      }
    }

    return null;
  }

  async activatePhysicalCard(authData: AuthData, dto: ActivatePhysicalCardDto) {
    const user = await this.userRepository.findOne({
      where: { userId: authData.id + '' },
    });

    try {
      const data = await this.bridgecardService.activatePhysicalCards(
        {
          cardholder_id: user.id,
          card_type: dto.cardType,
          card_brand: dto.cardBrand,
          card_currency: dto.cardCurrency,
          card_token_number: dto.cardTokenNumber,
          meta_data: {
            user_id: user.userId,
          },
        },
        user?.id,
      );

      return data;
    } catch (error) {
      throw new UnprocessableEntityException(error);
    }
  }

  async getUserCards(userId: string) {
    const user = await this.userRepository.findOne({
      where: { userId: userId, deletedAt: IsNull() },
      relations: { cards: true },
      order: { createdAt: -1, cards: { createdAt: -1 } },
    });

    return { message: 'success', data: user?.cards };
  }

  async getCardDetailsFromBridge(cardId: string, key: string, userId: string) {
    try {
      let data = await this.bridgecardService.getCardDetailsFromBridge(
        cardId,
        userId,
      );

      if (data) {
        data = this.encryptionService.encryptSensitiveData(data, key);

        return data;
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  async getCardDetails(cardId: string) {
    try {
      const card = await this.cardRepository.findOne({
        where: { id: cardId },
        relations: { user: true },
      });

      if (!card) {
        return null;
      }

      let data = await this.midenService.getCardDetails(cardId);
      const encryptionKey = card.user.userId + card.user?.id;
      const decryptedDetails = this.encryptionService.decryptMidenAES(
        data.data.secureCardDetails,
        config.miden.clientId,
      );

      await this.cardRepository.update(
        { id: card.id },
        {
          cardNumber: this.encryptionService.encryptDeep(
            encryptionKey,
            decryptedDetails.CardNumber,
          ),
          cvv: this.encryptionService.encryptDeep(
            encryptionKey,
            decryptedDetails.SecurityCode,
          ),
          expiryMonth: decryptedDetails.Expiration.split('-')[1],
          expiryYear: decryptedDetails.Expiration.split('-')[0],
          billingAddress: {
            state: data.data.billingDetails.state,
            billing_city: data.data.billingDetails.city,
            country_code: data.data.billingDetails.country,
            billing_country: data.data.billingDetails.country,
            billing_address1: data.data.billingDetails.addressLine1,
            billing_zip_code: data.data.billingDetails.zipCode,
            country: data.data.billingDetails.country,
          } as any,
        },
      );

      return data;
    } catch (e) {
      throw new UnprocessableEntityException(e);
    }
  }

  async getCardBalance(cardId: string) {
    const card = await this.cardRepository.findOne({
      where: { id: cardId },
    });

    if (!card) {
      throw new BadRequestException('Card Not found');
    }

    try {
      const data = await this.bridgecardService.getCardBalance(
        card.cardId,
        card.user?.id,
      );

      return data;
    } catch (error) {
      throw new UnprocessableEntityException(error);
    }
  }

  async getSpendOtp(cardId: string, amount: number) {
    const card = await this.cardRepository.findOne({
      where: { id: cardId },
    });
    try {
      const data = await this.bridgecardService.sendOtp(
        card.cardId,
        amount,
        card.user?.id,
      );

      return data;
    } catch (error) {
      throw new UnprocessableEntityException(error);
    }
  }

  // HERE

  async initiateFunding(authData: AuthData, dto: InitiateFundCardDto) {
    checkPnd(authData);
    try {
      const card = await this.cardRepository.findOne({
        where: { id: dto.cardId },
      });

      const user = await this.userRepository.findOne({
        where: { userId: authData.id + '' },
      });

      if (!user) {
        throw new BadRequestException('User Not found');
      }

      if (!card) {
        throw new BadRequestException('Card Not found');
      }

      if (card.provider != CardProvider.MIDEN) {
        throw new BadRequestException(
          'Sorry you cannot fund this card at the moment, please try again later',
        );
      }

      await this.throttleCall(
        user.id,
        dto.currency,
        TransactionCategory.FUND_CARD,
        TransactionType.CREDIT,
      );

      const fee = await this.configurationsService.getConfigurationValue(
        dto.currency == 'USD'
          ? ConfigurationKeys.USD_CARD_FUNDING_FEE
          : ConfigurationKeys.NGN_CARD_FUNDING_FEE,
        dto.currency == 'USD' ? 1 : 1000,
      );
      let exchangeRate = 0;

      let amount = +dto.amount + +fee;
      if (dto.currency == 'USD') {
        try {
          exchangeRate = await this.currencyConverter.convertCurrencyUsd(
            1,
            ExchangeCategory.DEPOSIT,
          );
          amount = exchangeRate * Number(amount);
        } catch (e) {
          console.log(e);
          throw e;
        }
      }

      const reference = `card-${randomUUID()}`;
      const metadata: any = {
        reference,
        meta: dto.meta,
        method: dto.method,
        type: PaymentTypes.FUND,
        fee: +fee,
        amount: +dto.amount,
      };

      const txn = await this.transactionRepository.save({
        user: { id: user.id },
        amount: +dto.amount,
        reference,
        fee,
        currency: dto.currency,
        type: TransactionType.CREDIT,
        status: TransactionStatus.PENDING,
        paymentStatus: TransactionStatus.PENDING,
        category: TransactionCategory.FUND_CARD,
        card: { id: dto.cardId },
        narration: 'Card Funding',
        exchangeRate,
        metadata,
        provider: CardProvider.MIDEN,
      });

      await this.paymentCacheService.savePayment({
        source: PaymentTransactionSource.CARDS_SERVICE,
        userId: user.userId,
        currency: Currency.NGN,
        reference,
        amount,
        description: 'virtual card funding',
        returningRoutingKey: PaymentEvents.CARD_PAYMENT_STATUS,
        meta: metadata,
      });

      return txn;
    } catch (e) {
      console.log(e);
      throw new UnprocessableEntityException(e);
    }
  }

  async fundCard(reference: string, status: string) {
    if (status.toLowerCase() != 'success') {
      return await this.transactionRepository.update(
        {
          reference,
        },
        {
          status: TransactionStatus.FAILED,
          paymentStatus: TransactionStatus.FAILED,
        },
      );
    }
    const txn = await this.transactionRepository.findOne({
      where: {
        reference,
      },
      relations: { card: true, user: true },
    });

    if (!txn) {
      throw new BadRequestException('transaction not found');
    }

    if (txn.status != TransactionStatus.PENDING) {
      throw new BadRequestException('transaction already finalized');
    }

    await this.transactionRepository.update(
      {
        reference,
      },
      {
        paymentStatus: TransactionStatus.SUCCESS,
      },
    );

    // const amount = +txn.amount * 100 + ''; // as cents

    try {
      const data =
        // txn.currency == Currency.USD
        await this.midenService.topUp(
          txn.card.cardId,
          +txn.amount,
          txn.reference,
          txn.currency,
        );
      // : await this.bridgecardService.fundNgnCard(
      //     txn.card.cardId,
      //     +txn.amount,
      //     txn.id,
      //     txn.currency,
      //     txn.user?.id,

      if (data.isSuccessful) {
        await this.transactionRepository.update(
          { id: txn.id },
          { status: TransactionStatus.SUCCESS },
        );

        this.notificationService.cardFunding(txn, txn.user?.userId);
      }
      if (data.data?.availableBalance) {
        await this.cardRepository.update(
          {
            cardId: txn.card.cardId,
          },
          {
            balance: data.data?.availableBalance + '',
          },
        );
      }
      return data;
    } catch (error) {
      console.log(error);
      // throw new UnprocessableEntityException(error);
    } finally {
      // await this.updateCardBalance(txn.card.cardId);
      this.transactionQueue.add('funding', txn, {
        delay: 2000,
        // backoff: 5,
        attempts: 5,
      });
    }
  }

  async validateFundingTransaction(transaction: Transaction) {
    console.log('validateFundingTransaction', transaction.reference);
    if (transaction.status == TransactionStatus.SUCCESS) {
      return;
    }

    const data = await this.getCardTransactionStatus(transaction.reference);
    console.log('validateFundingTransaction', data);

    if (data.isSuccessful) {
      // await this.updateCardBalance(transaction.card.cardId);
      await this.transactionRepository.update(
        { id: transaction.id },
        { status: TransactionStatus.SUCCESS },
      );
      await this.topupSuccess(transaction);
    } else {
      if (String(data.responseMessage).toLowerCase().includes('not found')) {
        // this.finalizePayment(transaction,  PaymentTransactionStatus.S)
        switch (transaction.category) {
          case TransactionCategory.FUND_CARD:
            await this.fundCard(
              transaction.reference,
              TransactionStatus.SUCCESS,
            );
            break;
          // case TransactionCategory.CREATE_CARD:
          //   await this.createCard(
          //     transaction.reference,
          //     TransactionStatus.SUCCESS,
          //   );
          //   break;
          default:
            break;
        }
      }
      if (
        data.transaction?.transactionStatus == MidenCardTransactionStatus.FAILED
      ) {
        await this.transactionRepository.update(
          { id: transaction.id },
          { status: TransactionStatus.FAILED },
        );
        await this.topupFailed(transaction);
      }
    }
  }

  async validateWithdrawalTransaction(transaction: Transaction) {
    if (transaction.status == TransactionStatus.SUCCESS) {
      return;
    }

    const data = await this.getCardTransactionStatus(transaction.reference);
    console.log('response gotten', data);

    if (
      data.isSuccessful &&
      data?.transaction?.transactionStatus ==
        MidenCardTransactionStatus.APPROVED
    ) {
      await this.updateCardBalance(transaction.card.cardId);
      await this.unloadSuccess(transaction);
    } else if (
      !data.isSuccessful ||
      data?.transaction.transactionStatus == MidenCardTransactionStatus.FAILED
      // String(data?.transaction.).toLowerCase().includes('invalid transaction id')
    ) {
      await this.unloadFailed(transaction);
    }
  }

  async unloadCard(authData: AuthData, dto: UnloadCardDto) {
    checkPnd(authData);
    const card = await this.cardRepository.findOne({
      where: { id: dto.cardId },
    });

    const user = await this.userRepository.findOne({
      where: { userId: authData.id + '' },
    });

    if (dto.amount > parseFloat(card.balance) - 1) {
      throw new BadRequestException(
        'Insufficient balance. Please try again with a lower amount. Remember, your card must have at least 1 USD remaining after the withdrawal.',
      );
    }

    if (!user) {
      throw new BadRequestException('User Not found');
    }

    if (!card) {
      throw new BadRequestException('Card Not found');
    }

    if (card.provider != CardProvider.MIDEN) {
      throw new BadRequestException(
        'Sorry you cannot withdraw from this card at the moment, please try again later',
      );
    }

    await this.throttleCall(
      user.id,
      card.cardCurrency == 'USD' ? Currency.USD : Currency.NGN,
      TransactionCategory.WITHDRAW_CARD,
      TransactionType.DEBIT,
      5,
    );
    // await this.updateCardBalance(card.cardId);

    const fee = await this.configurationsService.getConfigurationValue(
      card.cardCurrency == 'USD'
        ? ConfigurationKeys.USD_CARD_WITHDRAWAL_FEE
        : ConfigurationKeys.NGN_CARD_WITHDRAWAL_FEE,
      card.cardCurrency == 'USD' ? 1 : 1000,
    );

    const amountWithFee = +dto.amount + +fee;

    if (+amountWithFee >= +card.balance) {
      throw new BadRequestException('Insufficient Balance');
    }

    const reference = `card-${randomUUID()}`;

    const transaction = await this.transactionRepository.save({
      userId: user.id,
      amount: +amountWithFee,
      fee: +fee,
      reference,
      currency: card.cardCurrency == 'USD' ? Currency.USD : Currency.NGN,
      type: TransactionType.DEBIT,
      status: TransactionStatus.PENDING,
      paymentStatus: TransactionStatus.PENDING,
      category: TransactionCategory.WITHDRAW_CARD,
      ...(dto.meta && {
        metadata: {
          ...dto.meta,
          method: 'cash-wallet',
          amount: amountWithFee,
          fee: +fee,
          inputAmount: +dto.amount,
        },
      }),
      card: { id: dto.cardId },
      narration: 'Card Withdrawal',
      provider: CardProvider.MIDEN,
    });

    const amount = +transaction.amount; // as cents
    try {
      const data = await this.midenService.withdraw(
        card.cardId,
        amount,
        reference,
        Currency.USD,
      );

      await this.transactionQueue.add('withdrawal', transaction, {
        delay: 2000,
        // backoff: 5,
        attempts: 5,
      });
      // await this.updateCardBalance(card.cardId);

      return data;
    } catch (error) {
      console.log(error);
      throw new UnprocessableEntityException(error);
    }
  }

  async updateCardBalance(cardId: string) {
    try {
      const card = await this.midenService.getCardDetailsMaskedPan(cardId);
      // console.log(card.airlinePaymentEnabled);
      await this.cardRepository.update(
        { cardId },
        {
          isActive: card.status.includes('Active'),
          isDeleted: card.terminated,
          terminationReason: card.terminationReason,
          balance: +card?.availableBalance + '',
        },
      );

      if (!card.airlinePaymentEnabled && !card.terminated) {
        await this.midenService.enableDisableAirlinePayment(cardId, true);
      }
    } catch (error) {
      console.log(error);
    }
  }

  async getCardTransactions(authData: AuthData, dto: GetCardTransactionsDto) {
    const user = await this.userRepository.findOne({
      where: { userId: authData.id + '' },
    });

    if (!user) {
      throw new BadRequestException('User Not found');
    }

    const transactions = await this.transactionRepository.findMany(
      {
        page: dto.page,
        limit: 30,
      },
      {
        where: { cardId: dto.cardId },
        relations: { user: false, card: false },
        order: { createdAt: -1 },
      },
    );

    return transactions;
  }

  async getBridgeCardTransactions(dto: GetCardTransactionsDto) {
    const card = await this.cardRepository.findOne({
      where: { id: dto.cardId },
    });

    try {
      const data = await this.bridgecardService.getBridgeCardTransactions(
        card.cardId,
        dto.page,
        card.user?.id,
      );

      return data;
    } catch (error) {
      throw new UnprocessableEntityException(error);
    }
  }

  async updateTransactionById(dto: UpdateTransactionByIdDto) {
    try {
      await this.transactionRepository.update(
        {
          id: dto.referenceId,
        },
        {
          status:
            dto.status == 'success'
              ? TransactionStatus.SUCCESS
              : TransactionStatus.FAILED,
          paymentStatus:
            dto.status == 'success'
              ? TransactionStatus.SUCCESS
              : TransactionStatus.FAILED,
        },
      );

      return true;
    } catch (e) {
      throw new UnprocessableEntityException(e);
    }
  }

  async getCardTransactionById(dto: GetCardTransactionByIdDto) {
    const transaction = await this.transactionRepository.findOne({
      where: { id: dto.transactionId },
      relations: { card: true, user: true },
    });

    return transaction;
  }

  async getBridgeCardTransactionById(dto: GetCardTransactionByIdDto) {
    try {
      return await this.bridgecardService.getCardTransactionsById(
        dto.cardId,
        dto.transactionId,
        '',
      );
    } catch (error) {
      throw new UnprocessableEntityException(error);
    }
  }

  async getCardTransactionStatus(
    reference: string,
  ): Promise<MidenCardTransactionResponse> {
    try {
      return await this.midenService.cardTransaction(reference);
    } catch (error) {
      return error;
      // throw new UnprocessableEntityException(error);
    }
  }

  async getAllCardholderCards(userId: string) {
    const user = await this.userRepository.findOne({
      where: {
        userId: userId,
      },
    });
    try {
      const data = await this.bridgecardService.getAllCardholderCards(
        user.cardHolderId,
        user.id,
      );

      return data;
    } catch (error) {
      throw new UnprocessableEntityException(error);
    }
  }

  async syncAllCardholderCards(userId: string) {
    const user = await this.userRepository.findOne({ where: { id: userId } });

    if (!user) {
      throw new BadRequestException('User Not found');
    }
    const data = await this.midenService.getCardCustomers(
      user.midenCardHolderId,
      user.email,
    );

    const cards = data?.customers?.length
      ? data?.customers[0]?.customerCards
      : [];

    console.log(cards);
    // return;
    for (const card of cards) {
      const cardId = card?.cardId;

      const savedCard = await this.cardRepository.findOne({
        where: { cardId: cardId },
        relations: { user: true },
      });

      if (card.terminated && !savedCard.isDeleted) {
        this.notificationService.cardTerminated(
          {
            // amount: data?.amount,
            currency: Currency.USD,
            // createdAt: data?.createdAt,
            // currency: data?.currencyCode,
            // reference: data?.transactionReference,
            // merchant: data?.cardAcceptorName,
            reason: card.terminationReason,
          },
          savedCard?.user?.userId,
        );
      }

      if (savedCard) {
        await this.cardRepository.update(
          { cardId: cardId },
          {
            isDeleted: card.terminated,
            terminationReason: card.terminationReason,
          },
        );
        continue;
      }
      this.saveCardDetails(cardId, user);

      // const cardDetails = await this.getCardDetailsFromBridge(
      //   cardId,
      //   user.userId + user.cardHolderId,
      //   user.id,
      // );

      // if (cardDetails?.is_deleted) {
      //   await this.cardRepository.save({
      //     userId: user.id,
      //     balance: +cardDetails.balance / 100 + '',
      //     currentLimit: '10000',
      //     billingAddress: cardDetails.billing_address,
      //     cardId: cardDetails.card_id,
      //     cardNumber: cardDetails.card_number,
      //     cardBrand: cardDetails.brand,
      //     cardType: cardDetails.card_type,
      //     cardCurrency: cardDetails.card_currency,
      //     lastFourDigits: cardDetails.last_4,
      //     cvv: cardDetails.cvv,
      //     expiryMonth: cardDetails.expiry_month,
      //     expiryYear: cardDetails.expiry_year,
      //     issuingAppId: cardDetails.issuing_app_id,
      //     isActive: card.is_active,
      //     isDeleted: card.is_deleted,
      //     cardPin: this.encryptionService.encryptDeep(
      //       user.userId + user.cardHolderId,
      //       '',
      //     ),
      //   });
      // }
    }
  }

  async freezeCard(dto: FreezeCardDto) {
    const card = await this.cardRepository.findOne({
      where: { id: dto.cardId },
      relations: { user: true },
    });

    if (!card) {
      throw new BadRequestException('Card Not found');
    }

    try {
      const data = await this.midenService.freezeUnfreezeCard(
        card.cardId,
        false,
      );

      await this.cardRepository.update({ id: card.id }, { isActive: false });
      return data;
    } catch (error) {
      throw new UnprocessableEntityException(error);
    }
  }

  async unfreezeCard(dto: UnfreezeCardDto) {
    const card = await this.cardRepository.findOne({
      where: { id: dto.cardId },
      relations: { user: true },
    });

    if (!card) {
      throw new BadRequestException('Card Not found');
    }

    if (card.isDeleted) {
      throw new BadRequestException(
        'Card has been terminated, you cannot unfreeze it',
      );
    }

    try {
      const data = await this.midenService.freezeUnfreezeCard(
        card.cardId,
        true,
      );

      await this.cardRepository.update({ id: card.id }, { isActive: true });
      return data;
    } catch (error) {
      throw new UnprocessableEntityException(error);
    }
  }

  async deleteCard(dto: DeleteCardDto) {
    const card = await this.cardRepository.findOne({
      where: { id: dto.cardId },
      relations: { user: true },
    });

    if (!card) {
      throw new BadRequestException('Card Not found');
    }

    try {
      const data = this.bridgecardService.deleteCard(
        card.cardId,
        card.user?.id,
      );

      await this.cardRepository.update(
        { id: dto.cardId },
        { isActive: false, isDeleted: true },
      );

      return data;
    } catch (e) {
      console.log(e);
      throw new UnprocessableEntityException(e);
    }
  }

  async updateCardPin(dto: UpdateCardPinDto) {
    try {
      const card = await this.cardRepository.findOne({
        where: { id: dto.cardId },
        relations: { user: true },
      });
      const encryptedPin = this.encryptionService.encryptBridgeCardPin(
        dto.newPin,
      );

      const data = await this.bridgecardService.updateCardPin(
        card.cardId,
        encryptedPin,
        card.user.id,
      );

      await this.cardRepository.update(
        { id: dto.cardId },
        {
          cardPin: this.encryptionService.encryptDeep(
            card.user.userId + card.user.cardHolderId,
            dto.newPin,
          ),
        },
      );

      return data;
    } catch (e) {
      console.log(e);
      throw new UnprocessableEntityException(e);
    }
  }

  async updateTransaction(referenceId: string, transactionStatus: string) {
    await this.transactionRepository.update(
      { id: referenceId },
      {
        status: transactionStatus,
      },
    );
  }

  async withdrawStatus(meta: any, status?: string) {
    try {
      const txn = await this.transactionRepository.findOne({
        where: {
          id: meta.referenceId,
        },
        relations: { card: true, user: true },
      });

      if (!txn) {
        throw new BadRequestException('transaction not found');
      }

      if (txn.status != TransactionStatus.PENDING) {
        throw new BadRequestException('transaction already finalized');
      }

      if (status == 'success') {
        return await this.transactionRepository.update(
          { id: txn.id },
          {
            status: TransactionStatus.SUCCESS,
            paymentStatus: TransactionStatus.SUCCESS,
          },
        );
      } else {
        await this.transactionRepository.update(
          {
            id: meta.referenceId,
          },
          {
            status: TransactionStatus.FAILED,
            paymentStatus: TransactionStatus.FAILED,
          },
        );
      }
    } catch (e) {
      console.log(e);
      console.log(e.status);
    }
  }

  async topupSuccess(txn: Transaction) {
    try {
      await this.transactionRepository.update(
        { id: txn.id },
        {
          status: TransactionStatus.SUCCESS,
        },
      );
      await this.notificationService.cardFunding(txn, txn.user?.userId);
      return true;
    } catch (e) {
      console.log(e);
      return false;
    }
  }

  async topupFailed(txn: Transaction) {
    const data = {
      type: txn.type,
      currency: txn.currency,
      amount: txn.amount,
      status: 'failed',
      reference: txn.id,
      meta: txn.metadata,
      method: txn.metadata.method,
      userId: txn.user.userId,
      transactionStatus: TransactionStatus.FAILED,
    };

    this.rmqService.send(Exchanges.PAYMENT, {
      key: PaymentEvents.REVERSE_TRANSACTION,
      data: {
        source: PaymentTransactionSource.CARDS_SERVICE,
        reference: txn.reference ?? txn.id,
        reason: 'Transaction failed from provider',
        returningRoutingKey: PaymentEvents.CARD_PAYMENT_STATUS,
      } as ReverseTransactionInterface,
    });

    await this.transactionRepository.update(
      { id: txn.id },
      {
        status: TransactionStatus.FAILED,
      },
    );

    return true;
  }

  async unloadSuccess(txn: Transaction) {
    if (!txn) {
      return;
    }
    let amount = +txn.amount; //remove fee to offset withdrawal fee
    let exchangeRate = 0;
    if (txn.currency == Currency.USD) {
      try {
        exchangeRate = await this.currencyConverter.convertCurrencyUsd(
          1,
          ExchangeCategory.WITHDRAWAL,
        );
        amount = exchangeRate * Number(amount);
      } catch (e) {
        console.log(e);
        throw e;
      }
    }

    const user = await this.userRepository.findOne({
      where: { id: txn.userId },
    });
    console.log('RETRY_WITHDRAWAL', user.userId);

    await this.rmqService.send(Exchanges.PAYMENT, {
      key: PaymentEvents.TOP_UP,
      data: {
        transaction: {
          source: PaymentTransactionSource.CARDS_SERVICE,
          reference: txn.reference ?? txn.id,
          amount,
          currency: txn.currency,
          userId: user.userId,
          type: PaymentTransactionType.CREDIT,
          description: 'Card Unload',
          meta: txn.metadata,
          returningRoutingKey: PaymentEvents.CARD_PAYMENT_STATUS,
        },
        returningRoutingKey: PaymentEvents.CARD_PAYMENT_STATUS,
      } as RequestWithdrawalDto,
    });

    if (txn.status != TransactionStatus.SUCCESS) {
      await this.transactionRepository.update(
        { id: txn.id },
        {
          status: TransactionStatus.SUCCESS,
          exchangeRate,
        },
      );
      // this.notificationService.cardWithdrawal(txn, txn.user?.userId);
    }
  }

  async unloadFailed(txn: Transaction) {
    await this.transactionRepository.update(
      { id: txn.id },
      {
        status: TransactionStatus.FAILED,
        paymentStatus: TransactionStatus.FAILED,
      },
    );
  }

  async updateTransactionBasedOnStatus( ) {
    const txn = await this.transactionRepository.find({
      where: {
        category: TransactionCategory.CHARGE,
        status: TransactionStatus.SUCCESS,
        narration: Like('%declined%'),

        createdAt: MoreThan(new Date(Date.now() - 1000 * 60 * 60 * 72)),
      },
      relations: {  },
    });
    console.log('txn', txn.length);
    for (const t of txn) {
      console.log('t', t.metadata.eventType);
      if(t.metadata.eventType == MidenWebhookEvent.PURCHASE_CARD_AUTH_APPROVED){
        await this.transactionRepository.update(
          { id: t.id },
          {
            status: TransactionStatus.SUCCESS,
            paymentStatus: TransactionStatus.SUCCESS,
            narration: t.metadata?.data?.cardAcceptorName,
          },
        );
      }
    }
  }

  async handleCharge(
    event: string,
    data: any,
    status: TransactionStatus,
    reason?: string,
  ) {
    console.log('handleCharge', data?.data?.amount, data?.data?.orderNumber);
    const card = await this.cardRepository.findOne({
      where: {
        cardId: data.cardId,
      },
      relations: { user: true },
    });

    if (
      // !data?.data?.orderNumber ||
      !(await this.transactionRepository.existsBy({
        reference: data?.data?.cardTransactionId ?? data?.eventId ?? '',
      }))
    ) {
      console.log('save transaction', data?.data?.amount);
      const transaction = await this.transactionRepository.save({
        userId: card.userId,
        amount: +(data?.data?.amount ?? data?.data?.authorizationAmount),
        fee: 0,
        currency: data.data?.currencyCode,
        type: TransactionType.CHARGE,
        category: TransactionCategory.CHARGE,
        provider: CardProvider.MIDEN,
        status: status,
        paymentStatus: status,
        card: { id: card.id },
        narration:
          reason ?? data?.data?.cardAcceptorName ?? data?.data?.merchantName,
        metadata: data,
        reference: data?.data?.cardTransactionId ?? data?.eventId,
      });
      await this.notificationService.cardPurchase(
        transaction,
        card.lastFourDigits,
        card.user?.userId,
      );
    }
    try {
      await this.updateCardBalance(card?.cardId);
    } catch (error) {
      console.log(error);
    }
  }

  async verifyTransaction(reference: string) {
    console.log('verifyTransaction', reference);
    const txn = await this.transactionRepository.findOne({
      where: {
        reference: reference,
      },
      relations: {
        card: true,
        user: true,
      },
    });
    if (
      txn.status != TransactionStatus.PENDING &&
      txn.status != TransactionStatus.PROCESSING
    ) {
      return;
    }
    try {
      const data = await this.midenService.cardTransaction(txn.reference);
      console.log('verifyTransaction', data);

      if (
        data.isSuccessful &&
        data.transaction.transactionStatus == MidenCardTransactionStatus.SUCCESS
      ) {
        await this.transactionRepository.update(
          { id: txn.id },
          { status: TransactionStatus.SUCCESS },
        );

        if (
          txn.type == TransactionType.CREDIT &&
          txn.category == TransactionCategory.CREATE_CARD
        ) {
          const card = await this.saveCardDetails(
            data.transaction?.cardId,
            txn.user, txn
          );

          if (!card) {
            this.createCard(txn.reference, TransactionStatus.SUCCESS);
            return;
          }

          await this.transactionRepository.update(
            { id: txn.id },
            {
              status: TransactionStatus.SUCCESS,
              card: { id: card.id },
            },
          );

          return;
        }

        this.cardRepository.update(
          { id: txn.cardId },
          { balance: data.transaction.balanceAfterTransaction + '' },
        );

        if (
          txn.type == TransactionType.DEBIT &&
          txn.category == TransactionCategory.WITHDRAW_CARD
        ) {
          this.unloadSuccess(txn);
        }

        await this.getCardBalance(txn.card.cardId);
      }
    } catch (error) {
      try {
        console.log(
          error,
          error.responseCode == '404',
          txn.type == TransactionType.CREDIT &&
            txn.category == TransactionCategory.CREATE_CARD,
        );

        if (error.responseCode == '404') {
          if (
            txn.type == TransactionType.CREDIT &&
            txn.category == TransactionCategory.CREATE_CARD
          ) {
            this.createCard(txn.reference, TransactionStatus.SUCCESS);
            return;
          }

          if (
            txn.type == TransactionType.CREDIT &&
            txn.category == TransactionCategory.FUND_CARD
          ) {
            this.fundCard(txn.reference, TransactionStatus.SUCCESS);
            return;
          }
        }
      } catch (error) {
        console.log(error);
      }
    }
  }

  async finalizePayment(transaction: Transaction, data: PaymentTransaction) {
    console.log(data.category, data.reference, data.walletType, {
      transaction,
      data,
    });
    switch (transaction.category) {
      case TransactionCategory.FUND_CARD:
        await this.fundCard(data.reference, TransactionStatus.SUCCESS);
        break;
      case TransactionCategory.CREATE_CARD:
        await this.createCard(data.reference, TransactionStatus.SUCCESS);
        break;
      case TransactionCategory.WITHDRAW_CARD:
        this.notificationService.cardWithdrawal(
          transaction,
          transaction.user?.userId,
        );

        break;
    }
  }
}
