import { DataSource, DataSourceOptions } from 'typeorm';
import config from '.';
import { Card } from 'src/card/entities/card.entity';
import { Transaction } from 'src/card/entities/transaction.entity';
import { UserData } from 'src/card/entities/user.entity';
import { Configuration } from 'src/configurations/entities/configuration.entity';
import { Log } from 'src/logs1/entities/log.entity';
import { Rate } from 'src/configurations/entities/rate.entity';
import { ResidentialAddress } from '../wema-card/entities/address';
import { WemaAccounts } from '../wema-card/entities/wema.account';
import { WemaCards } from '../wema-card/entities/wema-card.entity';
import { FundTransaction } from '../wema-card/entities/wema.fund.transactions';
import { CardCreationTransaction } from '../wema-card/entities/wema.card.creation.transactions';
import { WemaExternalTransactions } from '../wema-card/entities/wema.transaction.notifications';

const connection: {
  host?: string;
  username?: string;
  password?: string;
  database?: string;
  port?: number;
  url?: string;
} = {};

if (process.env.NODE_ENV === 'dev') {
  connection.host = process.env.DB_HOST;
  connection.username = process.env.DB_USER;
  connection.password = process.env.DB_PASSWORD;
  connection.database = process.env.DB_NAME;
  connection.port = Number(process.env.DB_PORT);
} else {
  connection.url = config.db.url;
}

export const typeOrmConfig: DataSourceOptions = {
  type: 'mysql',
  url: config.db.url,
  migrations: ['dist/db/migrations/*.js'],
  entities: [
    Card,
    Transaction,
    UserData,
    Configuration,
    Log,
    Rate,
    WemaAccounts,
    ResidentialAddress,
    WemaCards,
    FundTransaction,
    CardCreationTransaction,
    WemaExternalTransactions,
  ],
  logging: false,
  ssl: false,
  synchronize: config.env == 'development',
};

export const dataSource = new DataSource(typeOrmConfig);
