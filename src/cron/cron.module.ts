import { Module } from '@nestjs/common';
import { CronService } from './cron.service';
import { TransactionRepository } from 'src/card/repositories/transaction.repository';
import { CardRepository } from 'src/card/repositories/card.repository';
import { UserDataRepository } from 'src/card/repositories/user.repository';
import { CardProducer } from 'src/card/card.producer';
import { ConfigurationsModule } from 'src/configurations/configurations.module';
import { LogRepository } from 'src/card/repositories/log.repository';
import { BridgecardModule } from '@app/bridgecard';
import { MidenModule } from '@app/miden';
import { WemaCardModule } from '../wema-card/wema-card.module';
import { PaymentCacheModule } from '@crednet/utils';

@Module({
  imports: [
    BridgecardModule,
    ConfigurationsModule,
    MidenModule,
    PaymentCacheModule,
    WemaCardModule,
  ],
  providers: [
    CronService,
    TransactionRepository,
    CardRepository,
    UserDataRepository,
    TransactionRepository,
    LogRepository,
    CardProducer,
  ],
})
export class CronModule {}
