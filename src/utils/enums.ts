import config from 'src/config';

export enum TransactionType {
  DEBIT = 'DEBIT',
  CREDIT = 'CREDIT',
  CHARGE = 'CHARGE',
}

export enum CardTransactionType {
  CHARGE = 'CHARGE',
  FUNDING = 'FUNDING',
  WITHDRAWAL = 'WITHDRAWAL',
}

export enum TransactionStatus {
  PENDING = 'PENDING',
  // SUCCESS = 'SUCCESS',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  PROCESSING = 'PROCESSING',
  ABANDONED = 'ABANDONED',
}

export enum IdType {
  NIGERIAN_BVN_VERIFICATION = 'NIGERIAN_BVN_VERIFICATION',
  NIGERIAN_NIN = 'NIGERIAN_NIN',
  NIGERIAN_INTERNATIONAL_PASSPORT = 'NIGERIAN_INTERNATIONAL_PASSPORT',
  NIGERIAN_PVC = 'NIGERIAN_PVC',
  NIGERIAN_DRIVERS_LICENSE = 'NIGERIAN_DRIVERS_LICENSE',
}

export enum CardType {
  PHYSICAL = 'physical',
  VIRTUAL = 'virtual',
}

export enum BrideCardEvents {
  CREATION_FAILED = 'card_creation_event.failed',
  CREATION_SUCCESS = 'card_creation_event.successful',
  CREDIT_SUCCESS = 'card_credit_event.successful',
  CREDIT_FAILED = 'card_credit_event.failed',
  DEBIT_SUCCESS = 'card_debit_event.successful',
  DEBIT_FAILED = 'card_debit_event.declined',
  UNLOAD_SUCCESS = 'card_unload_event.successful',
  UNLOAD_FAILED = 'card_unload_event.failed',
  NAIRA_CREATION_FAILED = 'naira_card_creation_event.failed',
  NAIRA_CREATION_SUCCESS = 'naira_card_creation_event.successful',
  NAIRA_CREDIT_SUCCESS = 'naira_card_credit_event.successful',
  NAIRA_CREDIT_FAILED = 'naira_card_credit_event.failed',
  NAIRA_DEBIT_SUCCESS = 'naira_card_debit_event.successful',
  NAIRA_DEBIT_FAILED = 'naira_card_debit_event.declined',
  NAIRA_UNLOAD_SUCCESS = 'naira_card_unload_event.successful',
  NAIRA_UNLOAD_FAILED = 'naira_card_unload_event.failed',
}

export enum MidenCardTransactionStatus {
  PENDING = 'Pending',
  SUCCESS = 'Success',
  FAILED = 'Failed',
  APPROVED = 'Approved',
}

export enum MidenWebhookEvent {
  //   purchase.card.auth.approved:
  // Card authorization approved, reserving funds for the transaction but not yet transferring them.
  PURCHASE_CARD_AUTH_APPROVED = 'purchase.card.auth.approved',
  //   purchase.card.auth.declined:
  // Card authorization declined, preventing the transaction due to insufficient funds or other issues.
  PURCHASE_CARD_AUTH_DECLINED = 'purchase.card.auth.declined',
  //   purchase.card.auth.settled:
  // Authorized transaction settled, transferring reserved funds to the merchant.
  PURCHASE_CARD_AUTH_SETTLED = 'purchase.card.auth.settled',
  // purchase.card.cross-border.settled:
  // Cross-border transaction settled, transferring funds for an international purchase.

  PURCHASE_CARD_CROSS_BORDER_SETTLED = 'purchase.card.cross-border.settled',

  // purchase.card.cross-border.pending:
  // Cross-border transaction pending, awaiting settlement.
  // When you see purchase.card.cross-border.pending, it means that amount has been calculated for the international purchase, but it is currently on hold because there were not enough funds in the account at the time of the transaction. Instead of declining the transaction, it will remain pending and at the end of the month, the equivalent amount will be deducted from customer's wallet to settle the charge. This ensures that the purchase is processed even if customer's account balance was initially insufficient.

  PURCHASE_CARD_CROSS_BORDER_PENDING = 'purchase.card.cross-border.pending',

  // purchase.card.auth.declined.terminate:
  // Card authorization declined, resulting in card termination due to issues like insufficient funds or suspected fraud.
  PURCHASE_CARD_AUTH_DECLINED_TERMINATE = 'purchase.card.auth.declined.terminate',

  // purchase.card.auth.declined.block:
  // Card authorization declined, and the card is blocked due to issues like insufficient funds or suspected fraud.
  PURCHASE_CARD_AUTH_DECLINED_BLOCK = 'purchase.card.auth.declined.block',

  // purchase.card.auth.reversal.settled:
  // Previously authorized transaction reversed and settled, releasing reserved funds back to the cardholder's account.
  PURCHASE_CARD_AUTH_REVERSAL_SETTLED = 'purchase.card.auth.reversal.settled',

  // purchase.card.return.auth.approved:
  // This event signifies that a card authorization request for a return (refund) has been approved, allowing the return process to proceed.
  PURCHASE_CARD_RETURN_AUTH_APPROVED = 'purchase.card.return.auth.approved',

  // purchase.card.return.auth.settled:
  // This event indicates that an authorized return transaction has been settled, meaning the refund has been successfully processed and funds have been returned to the cardholder.
  PURCHASE_CARD_RETURN_AUTH_SETTLED = 'purchase.card.return.auth.settled',

  // purchase.card.auth.reversal.issuerexpiration:
  // This event occurs when an authorization reversal is issued due to the expiration of the authorization hold period, releasing the reserved funds back to the cardholder's account.
  PURCHASE_CARD_AUTH_REVERSAL_ISSUER_EXPIRATION = 'purchase.card.auth.reversal.issuerexpiration',

  // purchase.card.topup:
  // This event signifies that funds have been added to a card, increasing its available balance.
  PURCHASE_CARD_TOPUP = 'purchase.card.topup',

  // purchase.card.withdrawal:
  // This event indicates that funds have been withdrawn from the card, decreasing its available balance.
  PURCHASE_CARD_WITHDRAWAL = 'purchase.card.withdrawal',

  // purchase.card.terminate.regularize:
  // This event is triggered when a terminated card is successfully regularized.
  PURCHASE_CARD_TERMINATE_REGULARIZE = 'purchase.card.terminate.regularize',

  // purchase.card.terminated.refund.settled:
  // This event is triggered when a refund is successfully settled on a terminated card.
  PURCHASE_CARD_TERMINATED_REFUND_SETTLED = 'purchase.card.terminated.refund.settled',

  // purchase.card.decline-charge.settled:
  // This event occurs when a previously declined transaction charge has been settled, meaning the charge was successfully processed after the initial decline.
  PURCHASE_CARD_DECLINE_CHARGE_SETTLED = 'purchase.card.decline-charge.settled',

  PURCHASE_CARD_TERMINATE = 'purchase.card.terminate',

  PURCHASE_CARD_ISSUED = 'purchase.card.issued',
}

export enum MidenEventClass {
  //   CardBlock:
  // Events under this class indicate actions related to blocking or terminating a card, typically due to security concerns or user requests.
  CARD_BLOCK = 'CardBlock',
  //   Purchase:
  // Events in this class involve transactions where the card is used to make purchases, covering both authorizations and completions.
  PURCHASE = 'Purchase',
  //   Settlement:
  // This class includes events related to the finalization and settlement of transactions, ensuring that funds are transferred and transactions are completed.
  SETTLEMENT = 'Settlement',
  //   CardTopup:
  // Events classified under this type pertain to adding funds to a card, increasing its available balance.
  CARD_TOPUP = 'CardTopup',

  // CardWithdrawal:
  // This class includes events involving the withdrawal of funds from a card, reducing its available balance.

  CARD_WITHDRAWAL = 'CardWithdrawal',
  CROSS_BORDER = 'CrossBorder',
  DECLINES = 'Declines',
  CARD_EXPIRATION = 'CardExpiration',
  CARD_TERMINATION = 'CardTermination',
  REFUND_SETTLEMENT = 'RefundSettlement',
  // Purchase:
  // Events in this class involve transactions where the card is used to make purchases, covering both authorizations and completions.

  // Settlement:
  // This class includes events related to the finalization and settlement of transactions, ensuring that funds are transferred and transactions are completed.

  // CardTopup:
  // Events classified under this type pertain to adding funds to a card, increasing its available balance.

  // CardWithdrawal:
  // This class includes events involving the withdrawal of funds from a card, reducing its available balance.

  // CrossBorder:
  // Events in this class involve transactions that occur across international borders, often involving currency conversion and additional regulatory considerations.

  // Declines:
  // Events under this class indicate declined transactions, where card authorization requests were not approved for various reasons.

  // CardExpiration:
  // This class includes events related to the expiration of cards, providing information and alerts as the card approaches its expiry date.
}

export enum WemaWebhookEvent {
  CARD_CREATED = 'card.created',
  CARD_ACTIVATED = 'card.activated',
  CARD_FUNDED = 'card.funded',
  CARD_TRANSACTION = 'card.transaction',
  ACCOUNT_GENERATION = '2',
  ADDRESS_VALIDATION = '7',
  CARD_TRANSACTION_STATUS = '3',
  VIRTUAL_CARD_CREATION = '4',
  TRANSACTION_NOTIFICATION = '5',
}

export enum PaymentEvents {
  CARD_PAYMENT_STATUS = 'card_payment_status',
  WEMA_CARD_PAYMENT_STATUS = 'wema_card_payment_status',
  WEMA_CARD_CREATION_PAYMENT_STATUS = 'wema_card_creation_payment_status',
  TOP_UP = 'top-up',
  QUERY_TRANSACTION = 'query-transaction',
  REVERSE_TRANSACTION = 'reverse-transaction',
}

export enum PaymentEventTypes {
  PAYMENT_REQUEST_CREATE = 'card.payment.create',
  PAYMENT_REQUEST_FUND = 'card.payment.fund',
  PAYMENT_REQUEST_WITHDRAW = 'card.payment.withdraw',
  PAYMENT_RESPONSE_CREATE = 'card.payment.status.create',
  PAYMENT_RESPONSE_FUND = 'card.payment.status.fund',
  PAYMENT_RESPONSE_WITHDRAW = 'card.payment.status.withdraw',
  PAYMENT_RESPONSE_STATUS = 'card.payment.status.result',
}

// export enum PaymentRequestEventTypes {
//   PAYMENT_REQUEST_CREATE = 'cp.payment.charge',
//   REVERSE_TRANSACTION = 'reverse-transaction',
//   // PAYMENT_REQUEST_FUND = 'cp.payment.charge',
//   PAYMENT_REQUEST_WITHDRAW = 'cp.payment.topup',
//   PAYMENT_REQUEST_FINALIZE = 'cp.payment.finalize',
//   PAYMENT_RESPONSE_STATUS = 'cp.payment.status',
// }

export enum PaymentTypes {
  FUND = 'fund',
  CREATE = 'create',
  WITHDRAWAL = 'withdrawal',
}

export const LocalQueues = {
  TRANSACTION: 'transaction',
};

export const Queues = {
  PAYMENT_QUEUE: 'cp_queue',
  CARD_QUEUE: config.queue.base_queue,
};

export const Exchanges = {
  PAYMENT: 'payment',
  WEBHOOK: 'webhook',
  NOTIFICATION: 'notification',
};

export enum Events {
  UPDATE_FX_RATE = 'update_fx_rate',
  CREATE_LOGS = 'create_logs',
  SYNC_CARDHOLDER_CARDS = 'sync-cardholder-cards',
  UPDATE_BALANCE = 'update-balance',
  VALIDATE_FUNDING_TRANSACTION = 'validate-funding-transaction',
  VALIDATE_WITHDRAWAL_TRANSACTION = 'validate-withdrawal-transaction',
  VALIDATE_PENDING_TRANSACTION = 'validate-pending-transaction',
  CARDS_NOTIFICATION = 'send-cards-notification',
  RETRY_CREATION = 'retry-creation',
  RETRY_WITHDRAWAL = 'retry-withdrawal',
  REVERSE_TRANSACTION = 'reverse-transaction',
  REQUERY_FUND_WEMA_CARD = 'requery_fund_wema_card',
}

export enum ConfigurationKeys {
  DOLLAR_WITHDRAWAL_EXCHANGE__RATE = 'dollarWithdrawalExchangeRate',
  DOLLAR_DEPOSIT_EXCHANGE__RATE = 'dollarDepositExchangeRate',
  NGN_CARD_CREATION_FEE = 'ngnCardCreationFee',
  USD_CARD_CREATION_FEE = 'usdCardCreationFee',
  NGN_CARD_FUNDING_FEE = 'ngnCardFundingFee',
  USD_CARD_FUNDING_FEE = 'usdCardFundingFee',
  NGN_CARD_WITHDRAWAL_FEE = 'ngnCardWithdrawalFee',
  USD_CARD_WITHDRAWAL_FEE = 'usdCardWithdrawalFee',
}

export enum ExchangeCategory {
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
}

export enum TransactionCategory {
  CREATE_CARD = 'create_card',
  FUND_CARD = 'fund_card',
  WITHDRAW_CARD = 'withdraw_card',
  CHARGE = 'charge',
}

export enum NotificationTemplates {
  CARD_FUNDING = 'card_funded',
  CARD_PURCHASE = 'card_purchase',
  CARD_LOW_BALANCE = 'card_low_balance',
  CARD_WITHDRAWAL = 'card_withdrawal',
  CARD_CREATED = 'card_created',
  CARD_DECLINED = 'card_declined',
  CARD_TERMINATED = 'card_terminated',
}
